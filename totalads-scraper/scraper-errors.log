{"level":"error","message":"Error extracting tables: ReferenceError: __name is not defined","timestamp":"2025-06-26T19:26:10.053Z"}
{"level":"error","message":"Error extracting tables: ReferenceError: __name is not defined","timestamp":"2025-06-26T19:28:51.877Z"}
{"level":"error","message":"Error extracting tables: ReferenceError: __name is not defined","timestamp":"2025-06-26T19:39:41.670Z"}
{"level":"error","message":"Error extracting tables: ReferenceError: __name is not defined","timestamp":"2025-06-26T19:40:13.085Z"}
{"level":"error","message":"Error extracting tables: ReferenceError: __name is not defined","timestamp":"2025-06-26T19:40:47.929Z"}
{"level":"error","message":"Error extracting tables: ReferenceError: __name is not defined","timestamp":"2025-06-26T20:05:55.865Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:56:20)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:108:35)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:133:24)","timestamp":"2025-06-26T20:05:55.869Z"}
{"level":"error","message":"Error finding about and team pages: logger is not defined","name":"ReferenceError","stack":"ReferenceError: logger is not defined\n    at evaluate (evaluate at findAboutAndTeamPages (file:///Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:652:1656), <anonymous>:0:2040)\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:304:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async findAboutAndTeamPages (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:822:28)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:112:30)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:133:24)","timestamp":"2025-06-26T20:05:55.871Z"}
{"level":"error","message":"Error extracting tables: ReferenceError: __name is not defined","timestamp":"2025-06-26T20:06:44.804Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:56:20)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:108:35)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:133:24)","timestamp":"2025-06-26T20:06:44.810Z"}
{"level":"error","message":"Error finding about and team pages: logger is not defined","name":"ReferenceError","stack":"ReferenceError: logger is not defined\n    at evaluate (evaluate at findAboutAndTeamPages (file:///Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:652:1656), <anonymous>:0:2040)\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:304:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async findAboutAndTeamPages (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:822:28)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:112:30)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:133:24)","timestamp":"2025-06-26T20:06:44.812Z"}
{"level":"error","message":"Error extracting tables: ReferenceError: __name is not defined","timestamp":"2025-06-26T20:09:01.443Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:56:20)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:108:35)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:133:24)","timestamp":"2025-06-26T20:09:01.449Z"}
{"level":"error","message":"Error finding about and team pages: logger is not defined","name":"ReferenceError","stack":"ReferenceError: logger is not defined\n    at evaluate (evaluate at findAboutAndTeamPages (file:///Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:652:1656), <anonymous>:0:2040)\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:304:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async findAboutAndTeamPages (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:822:28)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:112:30)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:133:24)","timestamp":"2025-06-26T20:09:01.452Z"}
{"level":"error","message":"Error extracting tables: ReferenceError: __name is not defined","timestamp":"2025-06-26T20:10:28.628Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:56:20)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:108:35)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:133:24)","timestamp":"2025-06-26T20:10:28.630Z"}
{"level":"error","message":"Error finding about and team pages: logger is not defined","name":"ReferenceError","stack":"ReferenceError: logger is not defined\n    at evaluate (evaluate at findAboutAndTeamPages (file:///Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:652:1656), <anonymous>:0:2040)\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:304:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async findAboutAndTeamPages (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:822:28)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:112:30)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:133:24)","timestamp":"2025-06-26T20:10:28.632Z"}
{"level":"error","message":"Error extracting tables: ReferenceError: __name is not defined","timestamp":"2025-06-28T08:20:19.996Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:56:20)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:108:35)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:133:24)","timestamp":"2025-06-28T08:20:19.999Z"}
{"level":"error","message":"Error finding about and team pages: logger is not defined","name":"ReferenceError","stack":"ReferenceError: logger is not defined\n    at evaluate (evaluate at findAboutAndTeamPages (file:///Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:661:1656), <anonymous>:0:1937)\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:304:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async findAboutAndTeamPages (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:831:28)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:112:30)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:133:24)","timestamp":"2025-06-28T08:20:20.007Z"}
{"level":"error","message":"Error extracting tables: ReferenceError: __name is not defined","timestamp":"2025-06-28T08:20:40.669Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:56:20)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:108:35)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:133:24)","timestamp":"2025-06-28T08:20:40.671Z"}
{"level":"error","message":"Error finding about and team pages: logger is not defined","name":"ReferenceError","stack":"ReferenceError: logger is not defined\n    at evaluate (evaluate at findAboutAndTeamPages (file:///Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:661:1656), <anonymous>:0:1937)\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:304:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async findAboutAndTeamPages (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:831:28)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:112:30)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:133:24)","timestamp":"2025-06-28T08:20:40.672Z"}
{"code":"UNKNOWN_ERROR","context":"BrowserController.executeWithPage","level":"error","message":"Error in BrowserController.executeWithPage: Navigation timeout of 30000 ms exceeded","stack":"ScraperError: Error in BrowserController.executeWithPage: Navigation timeout of 30000 ms exceeded\n    at handleError (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/error-handler.ts:22:7)\n    at BrowserController.executeWithPage (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:137:7)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ScraperService.scrape (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:79:22)\n    at async ScraperController.scrapeUrl (/Users/<USER>/Desktop/totalads/totalads-scraper/src/api/scraper.controller.ts:27:22)\n    at async router.post.expressAsyncHandler.validationSchema (/Users/<USER>/Desktop/totalads/totalads-scraper/src/api/routes.ts:20:7)\n    at async file:///Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/totalads-shared@<EMAIL>+TotalAds+tota_hgkuyh6lxfp7ryn24pt53glqpu/node_modules/totalads-shared/dist/chunk-C2V5F7K2.mjs:24:16","timestamp":"2025-06-28T08:21:49.711Z"}
{"code":"UNKNOWN_ERROR","context":"ScraperService.scrape","level":"error","message":"Error in BrowserController.executeWithPage: Navigation timeout of 30000 ms exceeded","stack":"ScraperError: Error in BrowserController.executeWithPage: Navigation timeout of 30000 ms exceeded\n    at handleError (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/error-handler.ts:22:7)\n    at BrowserController.executeWithPage (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:137:7)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ScraperService.scrape (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:79:22)\n    at async ScraperController.scrapeUrl (/Users/<USER>/Desktop/totalads/totalads-scraper/src/api/scraper.controller.ts:27:22)\n    at async router.post.expressAsyncHandler.validationSchema (/Users/<USER>/Desktop/totalads/totalads-scraper/src/api/routes.ts:20:7)\n    at async file:///Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/totalads-shared@<EMAIL>+TotalAds+tota_hgkuyh6lxfp7ryn24pt53glqpu/node_modules/totalads-shared/dist/chunk-C2V5F7K2.mjs:24:16","timestamp":"2025-06-28T08:21:49.711Z"}
{"level":"error","message":"Error in scrape controller: Error in BrowserController.executeWithPage: Navigation timeout of 30000 ms exceeded","timestamp":"2025-06-28T08:21:49.711Z"}
{"level":"error","message":"Error extracting tables: ReferenceError: __name is not defined","timestamp":"2025-06-28T08:22:05.124Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:56:20)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:108:35)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:133:24)","timestamp":"2025-06-28T08:22:05.127Z"}
{"level":"error","message":"Error finding about and team pages: logger is not defined","name":"ReferenceError","stack":"ReferenceError: logger is not defined\n    at evaluate (evaluate at findAboutAndTeamPages (file:///Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:661:1656), <anonymous>:0:1937)\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:304:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async findAboutAndTeamPages (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:831:28)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:112:30)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:133:24)","timestamp":"2025-06-28T08:22:05.128Z"}
{"level":"error","message":"Error extracting tables: ReferenceError: __name is not defined","timestamp":"2025-06-28T08:22:32.303Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:56:20)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:108:35)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:133:24)","timestamp":"2025-06-28T08:22:32.308Z"}
{"level":"error","message":"Error finding about and team pages: logger is not defined","name":"ReferenceError","stack":"ReferenceError: logger is not defined\n    at evaluate (evaluate at findAboutAndTeamPages (file:///Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:661:1656), <anonymous>:0:1937)\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:304:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async findAboutAndTeamPages (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:831:28)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:112:30)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:133:24)","timestamp":"2025-06-28T08:22:32.311Z"}
{"level":"error","message":"Error extracting tables: ReferenceError: __name is not defined","timestamp":"2025-06-28T08:31:38.636Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:56:20)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:108:35)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:151:26)","timestamp":"2025-06-28T08:31:38.642Z"}
{"level":"error","message":"Error finding about and team pages: logger is not defined","name":"ReferenceError","stack":"ReferenceError: logger is not defined\n    at evaluate (evaluate at findAboutAndTeamPages (file:///Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:661:1656), <anonymous>:0:1937)\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:304:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async findAboutAndTeamPages (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:831:28)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:112:30)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:151:26)","timestamp":"2025-06-28T08:31:38.645Z"}
{"level":"error","message":"Error extracting tables: ReferenceError: __name is not defined","timestamp":"2025-06-28T08:34:28.313Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:56:20)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:108:35)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:151:26)","timestamp":"2025-06-28T08:34:28.324Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:56:20)\n    at async extractCompanyInfoFromSite (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:1117:25)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:120:33)","timestamp":"2025-06-28T08:34:29.882Z"}
{"level":"error","message":"Error extracting tables: ReferenceError: __name is not defined","timestamp":"2025-06-28T08:39:12.752Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:56:20)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:108:35)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:151:26)","timestamp":"2025-06-28T08:39:12.758Z"}
{"level":"error","message":"Error extracting tables: ReferenceError: __name is not defined","timestamp":"2025-06-28T08:40:10.722Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:56:20)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:108:35)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:151:26)","timestamp":"2025-06-28T08:40:10.724Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:56:20)\n    at async extractCompanyInfoFromSite (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:1117:25)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:120:33)","timestamp":"2025-06-28T08:40:14.792Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:56:20)\n    at async extractCompanyInfoFromSite (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:1117:25)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:120:33)","timestamp":"2025-06-28T08:40:18.678Z"}
{"level":"error","message":"Error extracting tables: ReferenceError: __name is not defined","timestamp":"2025-06-28T08:43:34.681Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:56:20)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:108:35)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:151:26)","timestamp":"2025-06-28T08:43:34.686Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:56:20)\n    at async extractCompanyInfoFromSite (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:1117:25)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:120:33)","timestamp":"2025-06-28T08:43:36.042Z"}
{"level":"error","message":"Error extracting tables: ReferenceError: __name is not defined","timestamp":"2025-06-28T13:01:32.426Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:56:20)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:108:35)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:151:26)","timestamp":"2025-06-28T13:01:32.431Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:56:20)\n    at async extractCompanyInfoFromSite (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:1117:25)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:120:33)","timestamp":"2025-06-28T13:01:33.842Z"}
{"level":"error","message":"Error extracting tables: ReferenceError: __name is not defined","timestamp":"2025-06-28T13:33:19.420Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:37:18)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:108:35)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:151:26)","timestamp":"2025-06-28T13:33:19.425Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:37:18)\n    at async extractCompanyInfoFromSite (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:1189:22)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:120:33)","timestamp":"2025-06-28T13:33:21.248Z"}
{"level":"error","message":"Error extracting tables: ReferenceError: __name is not defined","timestamp":"2025-06-28T14:08:36.699Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:37:18)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:108:35)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:151:26)","timestamp":"2025-06-28T14:08:36.705Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:37:18)\n    at async extractCompanyInfoFromSite (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:1189:22)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:120:33)","timestamp":"2025-06-28T14:08:37.820Z"}
{"level":"error","message":"Error extracting tables: ReferenceError: __name is not defined","timestamp":"2025-06-28T14:16:10.357Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:37:18)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:108:35)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:151:26)","timestamp":"2025-06-28T14:16:10.361Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:37:18)\n    at async extractCompanyInfoFromSite (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:1189:22)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:120:33)","timestamp":"2025-06-28T14:16:11.519Z"}
{"level":"error","message":"Error extracting tables: ReferenceError: __name is not defined","timestamp":"2025-06-28T14:24:48.720Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:37:18)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:108:35)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:151:26)","timestamp":"2025-06-28T14:24:48.723Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:37:18)\n    at async extractCompanyInfoFromSite (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:1189:22)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:120:33)","timestamp":"2025-06-28T14:24:50.028Z"}
{"level":"error","message":"Error extracting tables: ReferenceError: __name is not defined","timestamp":"2025-06-28T14:31:55.001Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:37:18)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:108:35)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:151:26)","timestamp":"2025-06-28T14:31:55.005Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:37:18)\n    at async extractCompanyInfoFromSite (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:1189:22)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:120:33)","timestamp":"2025-06-28T14:31:56.264Z"}
{"level":"error","message":"Error extracting tables: ReferenceError: __name is not defined","timestamp":"2025-06-28T14:33:01.777Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:37:18)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:108:35)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:151:26)","timestamp":"2025-06-28T14:33:01.779Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:37:18)\n    at async extractCompanyInfoFromSite (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:1189:22)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:120:33)","timestamp":"2025-06-28T14:33:03.161Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:37:18)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:108:35)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:151:26)","timestamp":"2025-06-28T14:36:33.581Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:37:18)\n    at async extractCompanyInfoFromSite (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:1189:22)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:120:33)","timestamp":"2025-06-28T14:36:35.187Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:36:18)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:108:35)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:151:26)","timestamp":"2025-06-28T14:38:34.425Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:36:18)\n    at async extractCompanyInfoFromSite (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:1188:22)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:120:33)","timestamp":"2025-06-28T14:38:36.021Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:36:18)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:108:35)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:151:26)","timestamp":"2025-06-28T14:42:30.233Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:36:18)\n    at async extractCompanyInfoFromSite (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:1189:22)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:120:33)","timestamp":"2025-06-28T14:42:31.486Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:36:18)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:108:35)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:151:26)","timestamp":"2025-06-28T14:47:05.815Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:36:18)\n    at async extractCompanyInfoFromSite (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:1189:22)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:120:33)","timestamp":"2025-06-28T14:47:06.924Z"}
