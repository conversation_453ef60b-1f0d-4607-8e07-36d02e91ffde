{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp\n  - Timeout: 30000ms\n  - Max concurrency: 5","timestamp":"2025-06-26T19:25:31.400Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-26T19:25:31.424Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-26T19:25:31.424Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-26T19:25:31.424Z"}
{"level":"info","message":"API request received to scrape URL: https://en.wikipedia.org/wiki/Web_scraping","timestamp":"2025-06-26T19:26:05.655Z"}
{"level":"info","message":"Starting scrape operation for URL: https://en.wikipedia.org/wiki/Web_scraping","timestamp":"2025-06-26T19:26:05.656Z"}
{"level":"info","message":"Initializing Puppeteer cluster...","timestamp":"2025-06-26T19:26:05.656Z"}
{"level":"info","message":"Cluster initialized with max concurrency: 5","timestamp":"2025-06-26T19:26:06.610Z"}
{"level":"info","message":"Executing task for URL: https://en.wikipedia.org/wiki/Web_scraping","timestamp":"2025-06-26T19:26:06.610Z"}
{"level":"info","message":"Navigation to https://en.wikipedia.org/wiki/Web_scraping successful","timestamp":"2025-06-26T19:26:10.011Z"}
{"level":"info","message":"Extracting title and description metadata","timestamp":"2025-06-26T19:26:10.012Z"}
{"level":"info","message":"Extracted metadata - Title: Web scraping - Wikipedia, Description: N/A","timestamp":"2025-06-26T19:26:10.014Z"}
{"level":"info","message":"Extracting links from page","timestamp":"2025-06-26T19:26:10.014Z"}
{"level":"info","message":"Extracted 431 links from the page","timestamp":"2025-06-26T19:26:10.015Z"}
{"level":"info","message":"Converting HTML to text","timestamp":"2025-06-26T19:26:10.015Z"}
{"level":"info","message":"Converted 44843 characters of HTML to text","timestamp":"2025-06-26T19:26:10.042Z"}
{"level":"info","message":"Extracting tables from page","timestamp":"2025-06-26T19:26:10.043Z"}
{"level":"error","message":"Error extracting tables: ReferenceError: __name is not defined","timestamp":"2025-06-26T19:26:10.053Z"}
{"level":"info","message":"Scrape completed for https://en.wikipedia.org/wiki/Web_scraping in 4407ms","timestamp":"2025-06-26T19:26:10.063Z"}
{"level":"info","message":"API request received to scrape URL: https://www.ezrankings.com/","timestamp":"2025-06-26T19:28:41.415Z"}
{"level":"info","message":"Starting scrape operation for URL: https://www.ezrankings.com/","timestamp":"2025-06-26T19:28:41.416Z"}
{"level":"info","message":"Executing task for URL: https://www.ezrankings.com","timestamp":"2025-06-26T19:28:41.416Z"}
{"level":"info","message":"Navigation to https://www.ezrankings.com successful","timestamp":"2025-06-26T19:28:51.836Z"}
{"level":"info","message":"Extracting title and description metadata","timestamp":"2025-06-26T19:28:51.837Z"}
{"level":"info","message":"Extracted metadata - Title: Best Digital Marketing Agency ..., Description: Achieve up to 5X conversions w...","timestamp":"2025-06-26T19:28:51.839Z"}
{"level":"info","message":"Extracting links from page","timestamp":"2025-06-26T19:28:51.839Z"}
{"level":"info","message":"Extracted 195 links from the page","timestamp":"2025-06-26T19:28:51.840Z"}
{"level":"info","message":"Converting HTML to text","timestamp":"2025-06-26T19:28:51.840Z"}
{"level":"info","message":"Converted 32257 characters of HTML to text","timestamp":"2025-06-26T19:28:51.873Z"}
{"level":"info","message":"Extracting tables from page","timestamp":"2025-06-26T19:28:51.873Z"}
{"level":"error","message":"Error extracting tables: ReferenceError: __name is not defined","timestamp":"2025-06-26T19:28:51.877Z"}
{"level":"info","message":"Scrape completed for https://www.ezrankings.com/ in 10473ms","timestamp":"2025-06-26T19:28:51.889Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp\n  - Timeout: 30000ms\n  - Max concurrency: 5","timestamp":"2025-06-26T19:37:09.087Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-26T19:37:09.120Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-26T19:37:09.121Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-26T19:37:09.121Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp\n  - Timeout: 30000ms\n  - Max concurrency: 5","timestamp":"2025-06-26T19:39:31.013Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-26T19:39:31.037Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-26T19:39:31.037Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-26T19:39:31.037Z"}
{"level":"info","message":"API request received to scrape URL: https://www.ezrankings.com/","timestamp":"2025-06-26T19:39:33.343Z"}
{"level":"info","message":"Starting scrape operation for URL: https://www.ezrankings.com/","timestamp":"2025-06-26T19:39:33.344Z"}
{"level":"info","message":"Initializing Puppeteer cluster...","timestamp":"2025-06-26T19:39:33.345Z"}
{"level":"info","message":"Cluster initialized with max concurrency: 5","timestamp":"2025-06-26T19:39:34.778Z"}
{"level":"info","message":"Executing task for URL: https://www.ezrankings.com","timestamp":"2025-06-26T19:39:34.779Z"}
{"level":"info","message":"Navigation to https://www.ezrankings.com successful","timestamp":"2025-06-26T19:39:41.622Z"}
{"level":"info","message":"Extracting title and description metadata","timestamp":"2025-06-26T19:39:41.623Z"}
{"level":"info","message":"Extracted metadata - Title: Best Digital Marketing Agency ..., Description: Achieve up to 5X conversions w...","timestamp":"2025-06-26T19:39:41.628Z"}
{"level":"info","message":"Extracting links from page","timestamp":"2025-06-26T19:39:41.628Z"}
{"level":"info","message":"Extracted 195 links from the page","timestamp":"2025-06-26T19:39:41.634Z"}
{"level":"info","message":"Converting HTML to text","timestamp":"2025-06-26T19:39:41.635Z"}
{"level":"info","message":"Converted 32256 characters of HTML to text","timestamp":"2025-06-26T19:39:41.660Z"}
{"level":"info","message":"Extracting tables from page","timestamp":"2025-06-26T19:39:41.660Z"}
{"level":"error","message":"Error extracting tables: ReferenceError: __name is not defined","timestamp":"2025-06-26T19:39:41.670Z"}
{"level":"info","message":"Extracting about data from: https://www.ezrankings.com","timestamp":"2025-06-26T19:39:41.671Z"}
{"level":"info","message":"Finding about and team pages from: https://www.ezrankings.com","timestamp":"2025-06-26T19:39:41.683Z"}
{"level":"info","message":"Found 1 about/team pages to extract","timestamp":"2025-06-26T19:39:41.687Z"}
{"level":"info","message":"Scrape completed for https://www.ezrankings.com/ in 10184ms","timestamp":"2025-06-26T19:39:43.528Z"}
{"level":"info","message":"API request received to scrape URL: https://hyperlinq.in/","timestamp":"2025-06-26T19:40:09.867Z"}
{"level":"info","message":"Starting scrape operation for URL: https://hyperlinq.in/","timestamp":"2025-06-26T19:40:09.867Z"}
{"level":"info","message":"Executing task for URL: https://hyperlinq.in","timestamp":"2025-06-26T19:40:09.867Z"}
{"level":"info","message":"Navigation to https://hyperlinq.in successful","timestamp":"2025-06-26T19:40:13.067Z"}
{"level":"info","message":"Extracting title and description metadata","timestamp":"2025-06-26T19:40:13.067Z"}
{"level":"info","message":"Extracted metadata - Title: Hyperlinq Technology | Marketi..., Description: Transform your marketing strat...","timestamp":"2025-06-26T19:40:13.068Z"}
{"level":"info","message":"Extracting links from page","timestamp":"2025-06-26T19:40:13.068Z"}
{"level":"info","message":"Extracted 60 links from the page","timestamp":"2025-06-26T19:40:13.069Z"}
{"level":"info","message":"Converting HTML to text","timestamp":"2025-06-26T19:40:13.069Z"}
{"level":"info","message":"Converted 7182 characters of HTML to text","timestamp":"2025-06-26T19:40:13.082Z"}
{"level":"info","message":"Extracting tables from page","timestamp":"2025-06-26T19:40:13.083Z"}
{"level":"error","message":"Error extracting tables: ReferenceError: __name is not defined","timestamp":"2025-06-26T19:40:13.085Z"}
{"level":"info","message":"Extracting about data from: https://hyperlinq.in","timestamp":"2025-06-26T19:40:13.086Z"}
{"level":"info","message":"Finding about and team pages from: https://hyperlinq.in","timestamp":"2025-06-26T19:40:13.091Z"}
{"level":"info","message":"Found 1 about/team pages to extract","timestamp":"2025-06-26T19:40:13.092Z"}
{"level":"info","message":"Scrape completed for https://hyperlinq.in/ in 5706ms","timestamp":"2025-06-26T19:40:15.573Z"}
{"level":"info","message":"API request received to scrape URL: https://www.ezrankings.com","timestamp":"2025-06-26T19:40:42.494Z"}
{"level":"info","message":"Starting scrape operation for URL: https://www.ezrankings.com","timestamp":"2025-06-26T19:40:42.494Z"}
{"level":"info","message":"Executing task for URL: https://www.ezrankings.com","timestamp":"2025-06-26T19:40:42.494Z"}
{"level":"info","message":"Navigation to https://www.ezrankings.com successful","timestamp":"2025-06-26T19:40:47.894Z"}
{"level":"info","message":"Extracting title and description metadata","timestamp":"2025-06-26T19:40:47.899Z"}
{"level":"info","message":"Extracted metadata - Title: Best Digital Marketing Agency ..., Description: Achieve up to 5X conversions w...","timestamp":"2025-06-26T19:40:47.904Z"}
{"level":"info","message":"Extracting links from page","timestamp":"2025-06-26T19:40:47.905Z"}
{"level":"info","message":"Extracted 195 links from the page","timestamp":"2025-06-26T19:40:47.906Z"}
{"level":"info","message":"Converting HTML to text","timestamp":"2025-06-26T19:40:47.907Z"}
{"level":"info","message":"Converted 32256 characters of HTML to text","timestamp":"2025-06-26T19:40:47.925Z"}
{"level":"info","message":"Extracting tables from page","timestamp":"2025-06-26T19:40:47.925Z"}
{"level":"error","message":"Error extracting tables: ReferenceError: __name is not defined","timestamp":"2025-06-26T19:40:47.929Z"}
{"level":"info","message":"Extracting about data from: https://www.ezrankings.com","timestamp":"2025-06-26T19:40:47.931Z"}
{"level":"info","message":"Finding about and team pages from: https://www.ezrankings.com","timestamp":"2025-06-26T19:40:47.944Z"}
{"level":"info","message":"Found 1 about/team pages to extract","timestamp":"2025-06-26T19:40:47.949Z"}
{"level":"info","message":"Scrape completed for https://www.ezrankings.com in 7148ms","timestamp":"2025-06-26T19:40:49.642Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp\n  - Timeout: 30000ms\n  - Max concurrency: 5","timestamp":"2025-06-26T19:59:22.668Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-26T19:59:22.693Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-26T19:59:22.694Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-26T19:59:22.694Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp\n  - Timeout: 30000ms\n  - Max concurrency: 5","timestamp":"2025-06-26T20:05:46.576Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-26T20:05:46.600Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-26T20:05:46.600Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-26T20:05:46.600Z"}
{"level":"info","message":"API request received to scrape URL: https://hyperlinq.in/","timestamp":"2025-06-26T20:05:51.110Z"}
{"level":"info","message":"Starting scrape operation for URL: https://hyperlinq.in/","timestamp":"2025-06-26T20:05:51.110Z"}
{"level":"info","message":"Initializing Puppeteer cluster...","timestamp":"2025-06-26T20:05:51.111Z"}
{"level":"info","message":"Cluster initialized with max concurrency: 5","timestamp":"2025-06-26T20:05:51.939Z"}
{"level":"info","message":"Executing task for URL: https://hyperlinq.in","timestamp":"2025-06-26T20:05:51.940Z"}
{"level":"info","message":"Navigation to https://hyperlinq.in successful","timestamp":"2025-06-26T20:05:55.825Z"}
{"level":"info","message":"Extracting title and description metadata","timestamp":"2025-06-26T20:05:55.825Z"}
{"level":"info","message":"Extracted metadata - Title: Hyperlinq Technology | Marketi..., Description: Transform your marketing strat...","timestamp":"2025-06-26T20:05:55.828Z"}
{"level":"info","message":"Extracting links from page","timestamp":"2025-06-26T20:05:55.828Z"}
{"level":"info","message":"Extracted 60 links from the page","timestamp":"2025-06-26T20:05:55.828Z"}
{"level":"info","message":"Converting HTML to text","timestamp":"2025-06-26T20:05:55.828Z"}
{"level":"info","message":"Converted 7182 characters of HTML to text","timestamp":"2025-06-26T20:05:55.848Z"}
{"level":"info","message":"Extracting tables from page","timestamp":"2025-06-26T20:05:55.848Z"}
{"level":"error","message":"Error extracting tables: ReferenceError: __name is not defined","timestamp":"2025-06-26T20:05:55.865Z"}
{"level":"info","message":"Extracting about data from: https://hyperlinq.in","timestamp":"2025-06-26T20:05:55.866Z"}
{"level":"info","message":"Extracting about page information","timestamp":"2025-06-26T20:05:55.867Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:56:20)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:108:35)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:133:24)","timestamp":"2025-06-26T20:05:55.869Z"}
{"level":"info","message":"Finding about and team pages from: https://hyperlinq.in","timestamp":"2025-06-26T20:05:55.869Z"}
{"level":"info","message":"Finding about and team pages from: https://hyperlinq.in","timestamp":"2025-06-26T20:05:55.869Z"}
{"level":"error","message":"Error finding about and team pages: logger is not defined","name":"ReferenceError","stack":"ReferenceError: logger is not defined\n    at evaluate (evaluate at findAboutAndTeamPages (file:///Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:652:1656), <anonymous>:0:2040)\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:304:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async findAboutAndTeamPages (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:822:28)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:112:30)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:133:24)","timestamp":"2025-06-26T20:05:55.871Z"}
{"level":"info","message":"Scrape completed for https://hyperlinq.in/ in 4774ms","timestamp":"2025-06-26T20:05:55.884Z"}
{"level":"info","message":"API request received to scrape URL: https://www.ezrankings.com/","timestamp":"2025-06-26T20:06:10.485Z"}
{"level":"info","message":"Starting scrape operation for URL: https://www.ezrankings.com/","timestamp":"2025-06-26T20:06:10.485Z"}
{"level":"info","message":"Executing task for URL: https://www.ezrankings.com","timestamp":"2025-06-26T20:06:10.486Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp\n  - Timeout: 30000ms\n  - Max concurrency: 5","timestamp":"2025-06-26T20:06:29.734Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-26T20:06:29.757Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-26T20:06:29.758Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-26T20:06:29.758Z"}
{"level":"info","message":"API request received to scrape URL: https://www.ezrankings.com/","timestamp":"2025-06-26T20:06:40.922Z"}
{"level":"info","message":"Starting scrape operation for URL: https://www.ezrankings.com/","timestamp":"2025-06-26T20:06:40.923Z"}
{"level":"info","message":"Initializing Puppeteer cluster...","timestamp":"2025-06-26T20:06:40.923Z"}
{"level":"info","message":"Cluster initialized with max concurrency: 5","timestamp":"2025-06-26T20:06:41.788Z"}
{"level":"info","message":"Executing task for URL: https://www.ezrankings.com","timestamp":"2025-06-26T20:06:41.788Z"}
{"level":"info","message":"Navigation to https://www.ezrankings.com successful","timestamp":"2025-06-26T20:06:44.758Z"}
{"level":"info","message":"Extracting title and description metadata","timestamp":"2025-06-26T20:06:44.758Z"}
{"level":"info","message":"Extracted metadata - Title: Best Digital Marketing Agency ..., Description: Achieve up to 5X conversions w...","timestamp":"2025-06-26T20:06:44.759Z"}
{"level":"info","message":"Extracting links from page","timestamp":"2025-06-26T20:06:44.759Z"}
{"level":"info","message":"Extracted 195 links from the page","timestamp":"2025-06-26T20:06:44.760Z"}
{"level":"info","message":"Converting HTML to text","timestamp":"2025-06-26T20:06:44.760Z"}
{"level":"info","message":"Converted 32256 characters of HTML to text","timestamp":"2025-06-26T20:06:44.784Z"}
{"level":"info","message":"Extracting tables from page","timestamp":"2025-06-26T20:06:44.784Z"}
{"level":"error","message":"Error extracting tables: ReferenceError: __name is not defined","timestamp":"2025-06-26T20:06:44.804Z"}
{"level":"info","message":"Extracting about data from: https://www.ezrankings.com","timestamp":"2025-06-26T20:06:44.805Z"}
{"level":"info","message":"Extracting about page information","timestamp":"2025-06-26T20:06:44.805Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:56:20)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:108:35)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:133:24)","timestamp":"2025-06-26T20:06:44.810Z"}
{"level":"info","message":"Finding about and team pages from: https://www.ezrankings.com","timestamp":"2025-06-26T20:06:44.810Z"}
{"level":"info","message":"Finding about and team pages from: https://www.ezrankings.com","timestamp":"2025-06-26T20:06:44.811Z"}
{"level":"error","message":"Error finding about and team pages: logger is not defined","name":"ReferenceError","stack":"ReferenceError: logger is not defined\n    at evaluate (evaluate at findAboutAndTeamPages (file:///Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:652:1656), <anonymous>:0:2040)\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:304:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async findAboutAndTeamPages (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:822:28)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:112:30)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:133:24)","timestamp":"2025-06-26T20:06:44.812Z"}
{"level":"info","message":"Scrape completed for https://www.ezrankings.com/ in 3901ms","timestamp":"2025-06-26T20:06:44.824Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp\n  - Timeout: 30000ms\n  - Max concurrency: 5","timestamp":"2025-06-26T20:07:01.160Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-26T20:07:01.182Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-26T20:07:01.182Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-26T20:07:01.182Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp\n  - Timeout: 30000ms\n  - Max concurrency: 5","timestamp":"2025-06-26T20:07:45.861Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-26T20:07:45.901Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-26T20:07:45.901Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-26T20:07:45.902Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp\n  - Timeout: 30000ms\n  - Max concurrency: 5","timestamp":"2025-06-26T20:08:06.846Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-26T20:08:06.868Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-26T20:08:06.868Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-26T20:08:06.868Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp\n  - Timeout: 30000ms\n  - Max concurrency: 5","timestamp":"2025-06-26T20:08:46.971Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-26T20:08:46.998Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-26T20:08:46.998Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-26T20:08:46.998Z"}
{"level":"info","message":"API request received to scrape URL: https://www.ezrankings.com/","timestamp":"2025-06-26T20:08:56.261Z"}
{"level":"info","message":"Starting scrape operation for URL: https://www.ezrankings.com/","timestamp":"2025-06-26T20:08:56.262Z"}
{"level":"info","message":"Initializing Puppeteer cluster...","timestamp":"2025-06-26T20:08:56.263Z"}
{"level":"info","message":"Cluster initialized with max concurrency: 5","timestamp":"2025-06-26T20:08:57.171Z"}
{"level":"info","message":"Executing task for URL: https://www.ezrankings.com","timestamp":"2025-06-26T20:08:57.171Z"}
{"level":"info","message":"Navigation to https://www.ezrankings.com successful","timestamp":"2025-06-26T20:09:01.375Z"}
{"level":"info","message":"Extracting title and description metadata","timestamp":"2025-06-26T20:09:01.378Z"}
{"level":"info","message":"Extracted metadata - Title: Best Digital Marketing Agency ..., Description: Achieve up to 5X conversions w...","timestamp":"2025-06-26T20:09:01.381Z"}
{"level":"info","message":"Extracting links from page","timestamp":"2025-06-26T20:09:01.383Z"}
{"level":"info","message":"Extracted 195 links from the page","timestamp":"2025-06-26T20:09:01.385Z"}
{"level":"info","message":"Converting HTML to text","timestamp":"2025-06-26T20:09:01.385Z"}
{"level":"info","message":"Converted 32256 characters of HTML to text","timestamp":"2025-06-26T20:09:01.417Z"}
{"level":"info","message":"Extracting tables from page","timestamp":"2025-06-26T20:09:01.418Z"}
{"level":"error","message":"Error extracting tables: ReferenceError: __name is not defined","timestamp":"2025-06-26T20:09:01.443Z"}
{"level":"info","message":"Extracting about data from: https://www.ezrankings.com","timestamp":"2025-06-26T20:09:01.444Z"}
{"level":"info","message":"Extracting about page information","timestamp":"2025-06-26T20:09:01.444Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:56:20)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:108:35)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:133:24)","timestamp":"2025-06-26T20:09:01.449Z"}
{"level":"info","message":"Finding about and team pages from: https://www.ezrankings.com","timestamp":"2025-06-26T20:09:01.450Z"}
{"level":"info","message":"Finding about and team pages from: https://www.ezrankings.com","timestamp":"2025-06-26T20:09:01.450Z"}
{"level":"error","message":"Error finding about and team pages: logger is not defined","name":"ReferenceError","stack":"ReferenceError: logger is not defined\n    at evaluate (evaluate at findAboutAndTeamPages (file:///Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:652:1656), <anonymous>:0:2040)\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:304:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async findAboutAndTeamPages (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:822:28)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:112:30)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:133:24)","timestamp":"2025-06-26T20:09:01.452Z"}
{"level":"info","message":"Scrape completed for https://www.ezrankings.com/ in 5208ms","timestamp":"2025-06-26T20:09:01.470Z"}
{"level":"info","message":"API request received to scrape URL: https://www.ezrankings.com/","timestamp":"2025-06-26T20:10:23.438Z"}
{"level":"info","message":"Starting scrape operation for URL: https://www.ezrankings.com/","timestamp":"2025-06-26T20:10:23.440Z"}
{"level":"info","message":"Executing task for URL: https://www.ezrankings.com","timestamp":"2025-06-26T20:10:23.441Z"}
{"level":"info","message":"Navigation to https://www.ezrankings.com successful","timestamp":"2025-06-26T20:10:28.575Z"}
{"level":"info","message":"Extracting title and description metadata","timestamp":"2025-06-26T20:10:28.575Z"}
{"level":"info","message":"Extracted metadata - Title: Best Digital Marketing Agency ..., Description: Achieve up to 5X conversions w...","timestamp":"2025-06-26T20:10:28.576Z"}
{"level":"info","message":"Extracting links from page","timestamp":"2025-06-26T20:10:28.576Z"}
{"level":"info","message":"Extracted 195 links from the page","timestamp":"2025-06-26T20:10:28.577Z"}
{"level":"info","message":"Converting HTML to text","timestamp":"2025-06-26T20:10:28.577Z"}
{"level":"info","message":"Converted 32256 characters of HTML to text","timestamp":"2025-06-26T20:10:28.623Z"}
{"level":"info","message":"Extracting tables from page","timestamp":"2025-06-26T20:10:28.624Z"}
{"level":"error","message":"Error extracting tables: ReferenceError: __name is not defined","timestamp":"2025-06-26T20:10:28.628Z"}
{"level":"info","message":"Extracting about data from: https://www.ezrankings.com","timestamp":"2025-06-26T20:10:28.628Z"}
{"level":"info","message":"Extracting about page information","timestamp":"2025-06-26T20:10:28.629Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:56:20)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:108:35)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:133:24)","timestamp":"2025-06-26T20:10:28.630Z"}
{"level":"info","message":"Finding about and team pages from: https://www.ezrankings.com","timestamp":"2025-06-26T20:10:28.631Z"}
{"level":"info","message":"Finding about and team pages from: https://www.ezrankings.com","timestamp":"2025-06-26T20:10:28.631Z"}
{"level":"error","message":"Error finding about and team pages: logger is not defined","name":"ReferenceError","stack":"ReferenceError: logger is not defined\n    at evaluate (evaluate at findAboutAndTeamPages (file:///Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:652:1656), <anonymous>:0:2040)\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:304:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async findAboutAndTeamPages (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:822:28)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:112:30)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:133:24)","timestamp":"2025-06-26T20:10:28.632Z"}
{"level":"info","message":"Scrape completed for https://www.ezrankings.com/ in 5202ms","timestamp":"2025-06-26T20:10:28.643Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp\n  - Timeout: 30000ms\n  - Max concurrency: 5","timestamp":"2025-06-28T08:19:30.320Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T08:19:30.351Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T08:19:30.351Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-28T08:19:30.351Z"}
{"level":"info","message":"API request received to scrape URL: https://www.example.com","timestamp":"2025-06-28T08:20:15.835Z"}
{"level":"info","message":"Starting scrape operation for URL: https://www.example.com","timestamp":"2025-06-28T08:20:15.836Z"}
{"level":"info","message":"Initializing Puppeteer cluster...","timestamp":"2025-06-28T08:20:15.836Z"}
{"level":"info","message":"Cluster initialized with max concurrency: 5","timestamp":"2025-06-28T08:20:17.293Z"}
{"level":"info","message":"Executing task for URL: https://www.example.com","timestamp":"2025-06-28T08:20:17.293Z"}
{"level":"info","message":"Navigation to https://www.example.com successful","timestamp":"2025-06-28T08:20:19.958Z"}
{"level":"info","message":"Extracting title and description metadata","timestamp":"2025-06-28T08:20:19.958Z"}
{"level":"info","message":"Extracted metadata - Title: Example Domain, Description: N/A","timestamp":"2025-06-28T08:20:19.965Z"}
{"level":"info","message":"Extracting links from page","timestamp":"2025-06-28T08:20:19.966Z"}
{"level":"info","message":"Extracted 1 links from the page","timestamp":"2025-06-28T08:20:19.967Z"}
{"level":"info","message":"Converting HTML to text","timestamp":"2025-06-28T08:20:19.967Z"}
{"level":"info","message":"Converted 228 characters of HTML to text","timestamp":"2025-06-28T08:20:19.978Z"}
{"level":"info","message":"Extracting tables from page","timestamp":"2025-06-28T08:20:19.978Z"}
{"level":"error","message":"Error extracting tables: ReferenceError: __name is not defined","timestamp":"2025-06-28T08:20:19.996Z"}
{"level":"info","message":"Extracting about data from: https://www.example.com","timestamp":"2025-06-28T08:20:19.997Z"}
{"level":"info","message":"Extracting about page information","timestamp":"2025-06-28T08:20:19.997Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:56:20)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:108:35)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:133:24)","timestamp":"2025-06-28T08:20:19.999Z"}
{"level":"info","message":"Finding about and team pages from: https://www.example.com","timestamp":"2025-06-28T08:20:19.999Z"}
{"level":"info","message":"Finding about and team pages from: https://www.example.com","timestamp":"2025-06-28T08:20:19.999Z"}
{"level":"error","message":"Error finding about and team pages: logger is not defined","name":"ReferenceError","stack":"ReferenceError: logger is not defined\n    at evaluate (evaluate at findAboutAndTeamPages (file:///Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:661:1656), <anonymous>:0:1937)\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:304:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async findAboutAndTeamPages (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:831:28)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:112:30)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:133:24)","timestamp":"2025-06-28T08:20:20.007Z"}
{"level":"info","message":"Scrape completed for https://www.example.com in 4182ms","timestamp":"2025-06-28T08:20:20.018Z"}
{"level":"info","message":"API request received to scrape URL: https://html.com/tables/rowspan-colspan/","timestamp":"2025-06-28T08:20:35.354Z"}
{"level":"info","message":"Starting scrape operation for URL: https://html.com/tables/rowspan-colspan/","timestamp":"2025-06-28T08:20:35.354Z"}
{"level":"info","message":"Executing task for URL: https://html.com/tables/rowspan-colspan","timestamp":"2025-06-28T08:20:35.354Z"}
{"level":"info","message":"Navigation to https://html.com/tables/rowspan-colspan successful","timestamp":"2025-06-28T08:20:40.652Z"}
{"level":"info","message":"Extracting title and description metadata","timestamp":"2025-06-28T08:20:40.652Z"}
{"level":"info","message":"Extracted metadata - Title: Just a moment..., Description: N/A","timestamp":"2025-06-28T08:20:40.654Z"}
{"level":"info","message":"Extracting links from page","timestamp":"2025-06-28T08:20:40.654Z"}
{"level":"info","message":"Extracted 1 links from the page","timestamp":"2025-06-28T08:20:40.655Z"}
{"level":"info","message":"Converting HTML to text","timestamp":"2025-06-28T08:20:40.655Z"}
{"level":"info","message":"Converted 370 characters of HTML to text","timestamp":"2025-06-28T08:20:40.667Z"}
{"level":"info","message":"Extracting tables from page","timestamp":"2025-06-28T08:20:40.667Z"}
{"level":"error","message":"Error extracting tables: ReferenceError: __name is not defined","timestamp":"2025-06-28T08:20:40.669Z"}
{"level":"info","message":"Extracting about data from: https://html.com/tables/rowspan-colspan","timestamp":"2025-06-28T08:20:40.670Z"}
{"level":"info","message":"Extracting about page information","timestamp":"2025-06-28T08:20:40.670Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:56:20)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:108:35)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:133:24)","timestamp":"2025-06-28T08:20:40.671Z"}
{"level":"info","message":"Finding about and team pages from: https://html.com/tables/rowspan-colspan","timestamp":"2025-06-28T08:20:40.671Z"}
{"level":"info","message":"Finding about and team pages from: https://html.com/tables/rowspan-colspan","timestamp":"2025-06-28T08:20:40.671Z"}
{"level":"error","message":"Error finding about and team pages: logger is not defined","name":"ReferenceError","stack":"ReferenceError: logger is not defined\n    at evaluate (evaluate at findAboutAndTeamPages (file:///Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:661:1656), <anonymous>:0:1937)\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:304:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async findAboutAndTeamPages (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:831:28)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:112:30)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:133:24)","timestamp":"2025-06-28T08:20:40.672Z"}
{"level":"info","message":"Scrape completed for https://html.com/tables/rowspan-colspan/ in 5326ms","timestamp":"2025-06-28T08:20:40.680Z"}
{"level":"info","message":"API request received to scrape URL: https://www.w3schools.com/html/html_tables.asp","timestamp":"2025-06-28T08:21:19.509Z"}
{"level":"info","message":"Starting scrape operation for URL: https://www.w3schools.com/html/html_tables.asp","timestamp":"2025-06-28T08:21:19.509Z"}
{"level":"info","message":"Executing task for URL: https://www.w3schools.com/html/html_tables.asp","timestamp":"2025-06-28T08:21:19.509Z"}
{"code":"UNKNOWN_ERROR","context":"BrowserController.executeWithPage","level":"error","message":"Error in BrowserController.executeWithPage: Navigation timeout of 30000 ms exceeded","stack":"ScraperError: Error in BrowserController.executeWithPage: Navigation timeout of 30000 ms exceeded\n    at handleError (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/error-handler.ts:22:7)\n    at BrowserController.executeWithPage (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:137:7)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ScraperService.scrape (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:79:22)\n    at async ScraperController.scrapeUrl (/Users/<USER>/Desktop/totalads/totalads-scraper/src/api/scraper.controller.ts:27:22)\n    at async router.post.expressAsyncHandler.validationSchema (/Users/<USER>/Desktop/totalads/totalads-scraper/src/api/routes.ts:20:7)\n    at async file:///Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/totalads-shared@<EMAIL>+TotalAds+tota_hgkuyh6lxfp7ryn24pt53glqpu/node_modules/totalads-shared/dist/chunk-C2V5F7K2.mjs:24:16","timestamp":"2025-06-28T08:21:49.711Z"}
{"code":"UNKNOWN_ERROR","context":"ScraperService.scrape","level":"error","message":"Error in BrowserController.executeWithPage: Navigation timeout of 30000 ms exceeded","stack":"ScraperError: Error in BrowserController.executeWithPage: Navigation timeout of 30000 ms exceeded\n    at handleError (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/error-handler.ts:22:7)\n    at BrowserController.executeWithPage (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:137:7)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ScraperService.scrape (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:79:22)\n    at async ScraperController.scrapeUrl (/Users/<USER>/Desktop/totalads/totalads-scraper/src/api/scraper.controller.ts:27:22)\n    at async router.post.expressAsyncHandler.validationSchema (/Users/<USER>/Desktop/totalads/totalads-scraper/src/api/routes.ts:20:7)\n    at async file:///Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/totalads-shared@<EMAIL>+TotalAds+tota_hgkuyh6lxfp7ryn24pt53glqpu/node_modules/totalads-shared/dist/chunk-C2V5F7K2.mjs:24:16","timestamp":"2025-06-28T08:21:49.711Z"}
{"level":"error","message":"Error in scrape controller: Error in BrowserController.executeWithPage: Navigation timeout of 30000 ms exceeded","timestamp":"2025-06-28T08:21:49.711Z"}
{"level":"info","message":"API request received to scrape URL: https://en.wikipedia.org/wiki/Comparison_of_programming_languages","timestamp":"2025-06-28T08:22:02.906Z"}
{"level":"info","message":"Starting scrape operation for URL: https://en.wikipedia.org/wiki/Comparison_of_programming_languages","timestamp":"2025-06-28T08:22:02.906Z"}
{"level":"info","message":"Executing task for URL: https://en.wikipedia.org/wiki/Comparison_of_programming_languages","timestamp":"2025-06-28T08:22:02.906Z"}
{"level":"info","message":"Navigation to https://en.wikipedia.org/wiki/Comparison_of_programming_languages successful","timestamp":"2025-06-28T08:22:05.059Z"}
{"level":"info","message":"Extracting title and description metadata","timestamp":"2025-06-28T08:22:05.059Z"}
{"level":"info","message":"Extracted metadata - Title: Comparison of programming lang..., Description: N/A","timestamp":"2025-06-28T08:22:05.061Z"}
{"level":"info","message":"Extracting links from page","timestamp":"2025-06-28T08:22:05.061Z"}
{"level":"info","message":"Extracted 804 links from the page","timestamp":"2025-06-28T08:22:05.066Z"}
{"level":"info","message":"Converting HTML to text","timestamp":"2025-06-28T08:22:05.066Z"}
{"level":"info","message":"Converted 55426 characters of HTML to text","timestamp":"2025-06-28T08:22:05.121Z"}
{"level":"info","message":"Extracting tables from page","timestamp":"2025-06-28T08:22:05.121Z"}
{"level":"error","message":"Error extracting tables: ReferenceError: __name is not defined","timestamp":"2025-06-28T08:22:05.124Z"}
{"level":"info","message":"Extracting about data from: https://en.wikipedia.org/wiki/Comparison_of_programming_languages","timestamp":"2025-06-28T08:22:05.125Z"}
{"level":"info","message":"Extracting about page information","timestamp":"2025-06-28T08:22:05.125Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:56:20)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:108:35)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:133:24)","timestamp":"2025-06-28T08:22:05.127Z"}
{"level":"info","message":"Finding about and team pages from: https://en.wikipedia.org/wiki/Comparison_of_programming_languages","timestamp":"2025-06-28T08:22:05.127Z"}
{"level":"info","message":"Finding about and team pages from: https://en.wikipedia.org/wiki/Comparison_of_programming_languages","timestamp":"2025-06-28T08:22:05.127Z"}
{"level":"error","message":"Error finding about and team pages: logger is not defined","name":"ReferenceError","stack":"ReferenceError: logger is not defined\n    at evaluate (evaluate at findAboutAndTeamPages (file:///Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:661:1656), <anonymous>:0:1937)\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:304:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async findAboutAndTeamPages (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:831:28)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:112:30)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:133:24)","timestamp":"2025-06-28T08:22:05.128Z"}
{"level":"info","message":"Scrape completed for https://en.wikipedia.org/wiki/Comparison_of_programming_languages in 2233ms","timestamp":"2025-06-28T08:22:05.139Z"}
{"level":"info","message":"API request received to scrape URL: https://www.ezrankings.com/","timestamp":"2025-06-28T08:22:25.576Z"}
{"level":"info","message":"Starting scrape operation for URL: https://www.ezrankings.com/","timestamp":"2025-06-28T08:22:25.576Z"}
{"level":"info","message":"Executing task for URL: https://www.ezrankings.com","timestamp":"2025-06-28T08:22:25.577Z"}
{"level":"info","message":"Navigation to https://www.ezrankings.com successful","timestamp":"2025-06-28T08:22:32.196Z"}
{"level":"info","message":"Extracting title and description metadata","timestamp":"2025-06-28T08:22:32.199Z"}
{"level":"info","message":"Extracted metadata - Title: Best Digital Marketing Agency ..., Description: Achieve up to 5X conversions w...","timestamp":"2025-06-28T08:22:32.202Z"}
{"level":"info","message":"Extracting links from page","timestamp":"2025-06-28T08:22:32.203Z"}
{"level":"info","message":"Extracted 195 links from the page","timestamp":"2025-06-28T08:22:32.205Z"}
{"level":"info","message":"Converting HTML to text","timestamp":"2025-06-28T08:22:32.206Z"}
{"level":"info","message":"Converted 32257 characters of HTML to text","timestamp":"2025-06-28T08:22:32.293Z"}
{"level":"info","message":"Extracting tables from page","timestamp":"2025-06-28T08:22:32.295Z"}
{"level":"error","message":"Error extracting tables: ReferenceError: __name is not defined","timestamp":"2025-06-28T08:22:32.303Z"}
{"level":"info","message":"Extracting about data from: https://www.ezrankings.com","timestamp":"2025-06-28T08:22:32.304Z"}
{"level":"info","message":"Extracting about page information","timestamp":"2025-06-28T08:22:32.305Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:56:20)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:108:35)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:133:24)","timestamp":"2025-06-28T08:22:32.308Z"}
{"level":"info","message":"Finding about and team pages from: https://www.ezrankings.com","timestamp":"2025-06-28T08:22:32.308Z"}
{"level":"info","message":"Finding about and team pages from: https://www.ezrankings.com","timestamp":"2025-06-28T08:22:32.309Z"}
{"level":"error","message":"Error finding about and team pages: logger is not defined","name":"ReferenceError","stack":"ReferenceError: logger is not defined\n    at evaluate (evaluate at findAboutAndTeamPages (file:///Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:661:1656), <anonymous>:0:1937)\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:304:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async findAboutAndTeamPages (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:831:28)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:112:30)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:133:24)","timestamp":"2025-06-28T08:22:32.311Z"}
{"level":"info","message":"Scrape completed for https://www.ezrankings.com/ in 6753ms","timestamp":"2025-06-28T08:22:32.329Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp\n  - Timeout: 60000ms\n  - Max concurrency: 5","timestamp":"2025-06-28T08:31:28.518Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T08:31:28.549Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T08:31:28.549Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-28T08:31:28.549Z"}
{"level":"info","message":"API request received to scrape URL: https://www.ezrankings.com/","timestamp":"2025-06-28T08:31:33.994Z"}
{"level":"info","message":"Starting scrape operation for URL: https://www.ezrankings.com/","timestamp":"2025-06-28T08:31:33.994Z"}
{"level":"info","message":"Initializing Puppeteer cluster...","timestamp":"2025-06-28T08:31:33.995Z"}
{"level":"info","message":"Cluster initialized with max concurrency: 5","timestamp":"2025-06-28T08:31:35.180Z"}
{"level":"info","message":"Executing task for URL: https://www.ezrankings.com","timestamp":"2025-06-28T08:31:35.180Z"}
{"level":"info","message":"Navigation to https://www.ezrankings.com successful","timestamp":"2025-06-28T08:31:38.581Z"}
{"level":"info","message":"Extracting title and description metadata","timestamp":"2025-06-28T08:31:38.581Z"}
{"level":"info","message":"Extracted metadata - Title: Best Digital Marketing Agency ..., Description: Achieve up to 5X conversions w...","timestamp":"2025-06-28T08:31:38.583Z"}
{"level":"info","message":"Extracting links from page","timestamp":"2025-06-28T08:31:38.583Z"}
{"level":"info","message":"Extracted 195 links from the page","timestamp":"2025-06-28T08:31:38.584Z"}
{"level":"info","message":"Converting HTML to text","timestamp":"2025-06-28T08:31:38.584Z"}
{"level":"info","message":"Converted 32256 characters of HTML to text","timestamp":"2025-06-28T08:31:38.621Z"}
{"level":"info","message":"Extracting tables from page","timestamp":"2025-06-28T08:31:38.621Z"}
{"level":"error","message":"Error extracting tables: ReferenceError: __name is not defined","timestamp":"2025-06-28T08:31:38.636Z"}
{"level":"info","message":"Extracting about data from: https://www.ezrankings.com","timestamp":"2025-06-28T08:31:38.637Z"}
{"level":"info","message":"Extracting about page information","timestamp":"2025-06-28T08:31:38.637Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:56:20)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:108:35)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:151:26)","timestamp":"2025-06-28T08:31:38.642Z"}
{"level":"info","message":"Finding about and team pages from: https://www.ezrankings.com","timestamp":"2025-06-28T08:31:38.642Z"}
{"level":"info","message":"Finding about and team pages from: https://www.ezrankings.com","timestamp":"2025-06-28T08:31:38.643Z"}
{"level":"error","message":"Error finding about and team pages: logger is not defined","name":"ReferenceError","stack":"ReferenceError: logger is not defined\n    at evaluate (evaluate at findAboutAndTeamPages (file:///Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:661:1656), <anonymous>:0:1937)\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:304:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async findAboutAndTeamPages (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:831:28)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:112:30)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:151:26)","timestamp":"2025-06-28T08:31:38.645Z"}
{"level":"info","message":"Scrape completed for https://www.ezrankings.com/ in 4669ms","timestamp":"2025-06-28T08:31:38.663Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp\n  - Timeout: 60000ms\n  - Max concurrency: 5","timestamp":"2025-06-28T08:33:12.439Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T08:33:12.465Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T08:33:12.466Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-28T08:33:12.466Z"}
{"level":"info","message":"API request received to scrape URL: https://www.ezrankings.com/","timestamp":"2025-06-28T08:34:23.371Z"}
{"level":"info","message":"Starting scrape operation for URL: https://www.ezrankings.com/","timestamp":"2025-06-28T08:34:23.371Z"}
{"level":"info","message":"Initializing Puppeteer cluster...","timestamp":"2025-06-28T08:34:23.372Z"}
{"level":"info","message":"Cluster initialized with max concurrency: 5","timestamp":"2025-06-28T08:34:24.302Z"}
{"level":"info","message":"Executing task for URL: https://www.ezrankings.com","timestamp":"2025-06-28T08:34:24.302Z"}
{"level":"info","message":"Navigation to https://www.ezrankings.com successful","timestamp":"2025-06-28T08:34:28.234Z"}
{"level":"info","message":"Extracting title and description metadata","timestamp":"2025-06-28T08:34:28.244Z"}
{"level":"info","message":"Extracted metadata - Title: Best Digital Marketing Agency ..., Description: Achieve up to 5X conversions w...","timestamp":"2025-06-28T08:34:28.250Z"}
{"level":"info","message":"Extracting links from page","timestamp":"2025-06-28T08:34:28.250Z"}
{"level":"info","message":"Extracted 195 links from the page","timestamp":"2025-06-28T08:34:28.252Z"}
{"level":"info","message":"Converting HTML to text","timestamp":"2025-06-28T08:34:28.252Z"}
{"level":"info","message":"Converted 32256 characters of HTML to text","timestamp":"2025-06-28T08:34:28.281Z"}
{"level":"info","message":"Extracting tables from page","timestamp":"2025-06-28T08:34:28.282Z"}
{"level":"error","message":"Error extracting tables: ReferenceError: __name is not defined","timestamp":"2025-06-28T08:34:28.313Z"}
{"level":"info","message":"Extracting about data from: https://www.ezrankings.com","timestamp":"2025-06-28T08:34:28.315Z"}
{"level":"info","message":"Extracting about page information","timestamp":"2025-06-28T08:34:28.316Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:56:20)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:108:35)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:151:26)","timestamp":"2025-06-28T08:34:28.324Z"}
{"level":"info","message":"Finding about and team pages from: https://www.ezrankings.com","timestamp":"2025-06-28T08:34:28.324Z"}
{"level":"info","message":"Finding about and team pages from: https://www.ezrankings.com","timestamp":"2025-06-28T08:34:28.325Z"}
{"level":"info","message":"No about pages found in DOM, falling back to nested links...","timestamp":"2025-06-28T08:34:28.327Z"}
{"level":"info","message":"Found 1 unique about/team pages from nested links","timestamp":"2025-06-28T08:34:28.328Z"}
{"level":"info","message":"Found 1 about/team pages to extract","timestamp":"2025-06-28T08:34:28.328Z"}
{"level":"info","message":"Extracting company info from 1 pages","timestamp":"2025-06-28T08:34:28.328Z"}
{"level":"info","message":"Extracting about info from: https://www.ezrankings.com/about-us.html","timestamp":"2025-06-28T08:34:29.876Z"}
{"level":"info","message":"Extracting about page information","timestamp":"2025-06-28T08:34:29.877Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:56:20)\n    at async extractCompanyInfoFromSite (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:1117:25)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:120:33)","timestamp":"2025-06-28T08:34:29.882Z"}
{"level":"info","message":"Scrape completed for https://www.ezrankings.com/ in 6526ms","timestamp":"2025-06-28T08:34:29.897Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp\n  - Timeout: 60000ms\n  - Max concurrency: 5","timestamp":"2025-06-28T08:38:31.363Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T08:38:31.390Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T08:38:31.391Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-28T08:38:31.391Z"}
{"level":"info","message":"API request received to scrape URL: https://en.wikipedia.org/wiki/Comparison_of_programming_languages","timestamp":"2025-06-28T08:39:09.604Z"}
{"level":"info","message":"Starting scrape operation for URL: https://en.wikipedia.org/wiki/Comparison_of_programming_languages","timestamp":"2025-06-28T08:39:09.604Z"}
{"level":"info","message":"Initializing Puppeteer cluster...","timestamp":"2025-06-28T08:39:09.605Z"}
{"level":"info","message":"Cluster initialized with max concurrency: 5","timestamp":"2025-06-28T08:39:10.467Z"}
{"level":"info","message":"Executing task for URL: https://en.wikipedia.org/wiki/Comparison_of_programming_languages","timestamp":"2025-06-28T08:39:10.467Z"}
{"level":"info","message":"Navigation to https://en.wikipedia.org/wiki/Comparison_of_programming_languages successful","timestamp":"2025-06-28T08:39:12.683Z"}
{"level":"info","message":"Extracting title and description metadata","timestamp":"2025-06-28T08:39:12.684Z"}
{"level":"info","message":"Extracted metadata - Title: Comparison of programming lang..., Description: N/A","timestamp":"2025-06-28T08:39:12.686Z"}
{"level":"info","message":"Extracting links from page","timestamp":"2025-06-28T08:39:12.686Z"}
{"level":"info","message":"Extracted 804 links from the page","timestamp":"2025-06-28T08:39:12.690Z"}
{"level":"info","message":"Converting HTML to text","timestamp":"2025-06-28T08:39:12.690Z"}
{"level":"info","message":"Converted 55426 characters of HTML to text","timestamp":"2025-06-28T08:39:12.733Z"}
{"level":"info","message":"Extracting tables from page","timestamp":"2025-06-28T08:39:12.733Z"}
{"level":"error","message":"Error extracting tables: ReferenceError: __name is not defined","timestamp":"2025-06-28T08:39:12.752Z"}
{"level":"info","message":"Extracting about data from: https://en.wikipedia.org/wiki/Comparison_of_programming_languages","timestamp":"2025-06-28T08:39:12.754Z"}
{"level":"info","message":"Extracting about page information","timestamp":"2025-06-28T08:39:12.754Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:56:20)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:108:35)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:151:26)","timestamp":"2025-06-28T08:39:12.758Z"}
{"level":"info","message":"Finding about and team pages from: https://en.wikipedia.org/wiki/Comparison_of_programming_languages","timestamp":"2025-06-28T08:39:12.759Z"}
{"level":"info","message":"Finding about and team pages from: https://en.wikipedia.org/wiki/Comparison_of_programming_languages","timestamp":"2025-06-28T08:39:12.759Z"}
{"level":"info","message":"No about pages found in DOM, falling back to nested links...","timestamp":"2025-06-28T08:39:12.760Z"}
{"level":"info","message":"Found 0 unique about/team pages from nested links","timestamp":"2025-06-28T08:39:12.761Z"}
{"level":"info","message":"Scrape completed for https://en.wikipedia.org/wiki/Comparison_of_programming_languages in 3167ms","timestamp":"2025-06-28T08:39:12.771Z"}
{"level":"info","message":"API request received to scrape URL: https://www.w3schools.com/html/html_tables.asp","timestamp":"2025-06-28T08:39:52.389Z"}
{"level":"info","message":"Starting scrape operation for URL: https://www.w3schools.com/html/html_tables.asp","timestamp":"2025-06-28T08:39:52.389Z"}
{"level":"info","message":"Executing task for URL: https://www.w3schools.com/html/html_tables.asp","timestamp":"2025-06-28T08:39:52.390Z"}
{"level":"info","message":"Navigation to https://www.w3schools.com/html/html_tables.asp successful","timestamp":"2025-06-28T08:40:10.691Z"}
{"level":"info","message":"Extracting title and description metadata","timestamp":"2025-06-28T08:40:10.692Z"}
{"level":"info","message":"Extracted metadata - Title: W3Schools.com, Description: W3Schools offers free online t...","timestamp":"2025-06-28T08:40:10.693Z"}
{"level":"info","message":"Extracting links from page","timestamp":"2025-06-28T08:40:10.693Z"}
{"level":"info","message":"Extracted 774 links from the page","timestamp":"2025-06-28T08:40:10.695Z"}
{"level":"info","message":"Converting HTML to text","timestamp":"2025-06-28T08:40:10.695Z"}
{"level":"info","message":"Converted 44757 characters of HTML to text","timestamp":"2025-06-28T08:40:10.720Z"}
{"level":"info","message":"Extracting tables from page","timestamp":"2025-06-28T08:40:10.720Z"}
{"level":"error","message":"Error extracting tables: ReferenceError: __name is not defined","timestamp":"2025-06-28T08:40:10.722Z"}
{"level":"info","message":"Extracting about data from: https://www.w3schools.com/html/html_tables.asp","timestamp":"2025-06-28T08:40:10.723Z"}
{"level":"info","message":"Extracting about page information","timestamp":"2025-06-28T08:40:10.723Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:56:20)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:108:35)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:151:26)","timestamp":"2025-06-28T08:40:10.724Z"}
{"level":"info","message":"Finding about and team pages from: https://www.w3schools.com/html/html_tables.asp","timestamp":"2025-06-28T08:40:10.725Z"}
{"level":"info","message":"Finding about and team pages from: https://www.w3schools.com/html/html_tables.asp","timestamp":"2025-06-28T08:40:10.725Z"}
{"level":"info","message":"No about pages found in DOM, falling back to nested links...","timestamp":"2025-06-28T08:40:10.725Z"}
{"level":"info","message":"Found 3 unique about/team pages from nested links","timestamp":"2025-06-28T08:40:10.726Z"}
{"level":"info","message":"Found 3 about/team pages to extract","timestamp":"2025-06-28T08:40:10.726Z"}
{"level":"info","message":"Extracting company info from 3 pages","timestamp":"2025-06-28T08:40:10.726Z"}
{"level":"info","message":"Extracting about info from: https://www.w3schools.com/about/default.asp","timestamp":"2025-06-28T08:40:14.789Z"}
{"level":"info","message":"Extracting about page information","timestamp":"2025-06-28T08:40:14.789Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:56:20)\n    at async extractCompanyInfoFromSite (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:1117:25)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:120:33)","timestamp":"2025-06-28T08:40:14.792Z"}
{"level":"info","message":"Extracting about info from: https://www.w3schools.com/about/about_copyright.asp","timestamp":"2025-06-28T08:40:18.667Z"}
{"level":"info","message":"Extracting about page information","timestamp":"2025-06-28T08:40:18.667Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:56:20)\n    at async extractCompanyInfoFromSite (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:1117:25)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:120:33)","timestamp":"2025-06-28T08:40:18.678Z"}
{"level":"info","message":"Scrape completed for https://www.w3schools.com/html/html_tables.asp in 26353ms","timestamp":"2025-06-28T08:40:18.742Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp\n  - Timeout: 60000ms\n  - Max concurrency: 5","timestamp":"2025-06-28T08:42:10.055Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T08:42:10.086Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T08:42:10.086Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-28T08:42:10.086Z"}
{"level":"info","message":"API request received to scrape URL: https://www.ezrankings.com/","timestamp":"2025-06-28T08:43:29.746Z"}
{"level":"info","message":"Starting scrape operation for URL: https://www.ezrankings.com/","timestamp":"2025-06-28T08:43:29.747Z"}
{"level":"info","message":"Initializing Puppeteer cluster...","timestamp":"2025-06-28T08:43:29.748Z"}
{"level":"info","message":"Cluster initialized with max concurrency: 5","timestamp":"2025-06-28T08:43:30.639Z"}
{"level":"info","message":"Executing task for URL: https://www.ezrankings.com","timestamp":"2025-06-28T08:43:30.639Z"}
{"level":"info","message":"Navigation to https://www.ezrankings.com successful","timestamp":"2025-06-28T08:43:34.629Z"}
{"level":"info","message":"Extracting title and description metadata","timestamp":"2025-06-28T08:43:34.629Z"}
{"level":"info","message":"Extracted metadata - Title: Best Digital Marketing Agency ..., Description: Achieve up to 5X conversions w...","timestamp":"2025-06-28T08:43:34.630Z"}
{"level":"info","message":"Extracting links from page","timestamp":"2025-06-28T08:43:34.631Z"}
{"level":"info","message":"Extracted 195 links from the page","timestamp":"2025-06-28T08:43:34.632Z"}
{"level":"info","message":"Converting HTML to text","timestamp":"2025-06-28T08:43:34.632Z"}
{"level":"info","message":"Converted 32256 characters of HTML to text","timestamp":"2025-06-28T08:43:34.660Z"}
{"level":"info","message":"Extracting tables from page","timestamp":"2025-06-28T08:43:34.660Z"}
{"level":"error","message":"Error extracting tables: ReferenceError: __name is not defined","timestamp":"2025-06-28T08:43:34.681Z"}
{"level":"info","message":"Extracting about data from: https://www.ezrankings.com","timestamp":"2025-06-28T08:43:34.681Z"}
{"level":"info","message":"Extracting about page information","timestamp":"2025-06-28T08:43:34.682Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:56:20)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:108:35)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:151:26)","timestamp":"2025-06-28T08:43:34.686Z"}
{"level":"info","message":"Finding about and team pages from: https://www.ezrankings.com","timestamp":"2025-06-28T08:43:34.686Z"}
{"level":"info","message":"Finding about and team pages from: https://www.ezrankings.com","timestamp":"2025-06-28T08:43:34.687Z"}
{"level":"info","message":"No about pages found in DOM, falling back to nested links...","timestamp":"2025-06-28T08:43:34.688Z"}
{"level":"info","message":"Found 1 unique about/team pages from nested links","timestamp":"2025-06-28T08:43:34.689Z"}
{"level":"info","message":"Found 1 about/team pages to extract","timestamp":"2025-06-28T08:43:34.689Z"}
{"level":"info","message":"Extracting company info from 1 pages","timestamp":"2025-06-28T08:43:34.689Z"}
{"level":"info","message":"Extracting about info from: https://www.ezrankings.com/about-us.html","timestamp":"2025-06-28T08:43:36.029Z"}
{"level":"info","message":"Extracting about page information","timestamp":"2025-06-28T08:43:36.037Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:56:20)\n    at async extractCompanyInfoFromSite (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:1117:25)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:120:33)","timestamp":"2025-06-28T08:43:36.042Z"}
{"level":"info","message":"Scrape completed for https://www.ezrankings.com/ in 6317ms","timestamp":"2025-06-28T08:43:36.064Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp\n  - Timeout: 60000ms\n  - Max concurrency: 5","timestamp":"2025-06-28T12:56:35.768Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T12:56:35.797Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T12:56:35.798Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-28T12:56:35.798Z"}
{"level":"info","message":"API request received to scrape URL: https://www.ezrankings.com/","timestamp":"2025-06-28T13:01:24.385Z"}
{"level":"info","message":"Starting scrape operation for URL: https://www.ezrankings.com/","timestamp":"2025-06-28T13:01:24.385Z"}
{"level":"info","message":"Initializing Puppeteer cluster...","timestamp":"2025-06-28T13:01:24.386Z"}
{"level":"info","message":"Cluster initialized with max concurrency: 5","timestamp":"2025-06-28T13:01:25.375Z"}
{"level":"info","message":"Executing task for URL: https://www.ezrankings.com","timestamp":"2025-06-28T13:01:25.376Z"}
{"level":"info","message":"Navigation to https://www.ezrankings.com successful","timestamp":"2025-06-28T13:01:32.359Z"}
{"level":"info","message":"Extracting title and description metadata","timestamp":"2025-06-28T13:01:32.367Z"}
{"level":"info","message":"Extracted metadata - Title: Best Digital Marketing Agency ..., Description: Achieve up to 5X conversions w...","timestamp":"2025-06-28T13:01:32.372Z"}
{"level":"info","message":"Extracting links from page","timestamp":"2025-06-28T13:01:32.374Z"}
{"level":"info","message":"Extracted 195 links from the page","timestamp":"2025-06-28T13:01:32.376Z"}
{"level":"info","message":"Converting HTML to text","timestamp":"2025-06-28T13:01:32.376Z"}
{"level":"info","message":"Converted 32256 characters of HTML to text","timestamp":"2025-06-28T13:01:32.402Z"}
{"level":"info","message":"Extracting tables from page","timestamp":"2025-06-28T13:01:32.402Z"}
{"level":"error","message":"Error extracting tables: ReferenceError: __name is not defined","timestamp":"2025-06-28T13:01:32.426Z"}
{"level":"info","message":"Extracting about data from: https://www.ezrankings.com","timestamp":"2025-06-28T13:01:32.426Z"}
{"level":"info","message":"Extracting about page information","timestamp":"2025-06-28T13:01:32.427Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:56:20)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:108:35)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:151:26)","timestamp":"2025-06-28T13:01:32.431Z"}
{"level":"info","message":"Finding about and team pages from: https://www.ezrankings.com","timestamp":"2025-06-28T13:01:32.431Z"}
{"level":"info","message":"Finding about and team pages from: https://www.ezrankings.com","timestamp":"2025-06-28T13:01:32.432Z"}
{"level":"info","message":"No about pages found in DOM, falling back to nested links...","timestamp":"2025-06-28T13:01:32.433Z"}
{"level":"info","message":"Found 1 unique about/team pages from nested links","timestamp":"2025-06-28T13:01:32.434Z"}
{"level":"info","message":"Found 1 about/team pages to extract","timestamp":"2025-06-28T13:01:32.434Z"}
{"level":"info","message":"Extracting company info from 1 pages","timestamp":"2025-06-28T13:01:32.434Z"}
{"level":"info","message":"Extracting about info from: https://www.ezrankings.com/about-us.html","timestamp":"2025-06-28T13:01:33.816Z"}
{"level":"info","message":"Extracting about page information","timestamp":"2025-06-28T13:01:33.817Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:56:20)\n    at async extractCompanyInfoFromSite (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:1117:25)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:120:33)","timestamp":"2025-06-28T13:01:33.842Z"}
{"level":"info","message":"Scrape completed for https://www.ezrankings.com/ in 9485ms","timestamp":"2025-06-28T13:01:33.870Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp\n  - Timeout: 60000ms\n  - Max concurrency: 5","timestamp":"2025-06-28T13:12:13.835Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T13:12:13.849Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T13:12:13.850Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-28T13:12:13.850Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp\n  - Timeout: 60000ms\n  - Max concurrency: 5","timestamp":"2025-06-28T13:13:11.055Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T13:13:11.096Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T13:13:11.096Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-28T13:13:11.096Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp\n  - Timeout: 60000ms\n  - Max concurrency: 5","timestamp":"2025-06-28T13:26:32.709Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T13:26:32.853Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T13:26:32.854Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-28T13:26:32.854Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp\n  - Timeout: 60000ms\n  - Max concurrency: 5","timestamp":"2025-06-28T13:31:18.179Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T13:31:18.220Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T13:31:18.220Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-28T13:31:18.220Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp\n  - Timeout: 60000ms\n  - Max concurrency: 5","timestamp":"2025-06-28T13:32:01.880Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T13:32:01.920Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T13:32:01.920Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-28T13:32:01.920Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp\n  - Timeout: 60000ms\n  - Max concurrency: 5","timestamp":"2025-06-28T13:32:23.612Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T13:32:23.655Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T13:32:23.655Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-28T13:32:23.655Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp\n  - Timeout: 60000ms\n  - Max concurrency: 5","timestamp":"2025-06-28T13:32:38.289Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T13:32:38.334Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T13:32:38.335Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-28T13:32:38.335Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp\n  - Timeout: 60000ms\n  - Max concurrency: 5","timestamp":"2025-06-28T13:32:47.556Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T13:32:47.601Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T13:32:47.601Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-28T13:32:47.601Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp\n  - Timeout: 60000ms\n  - Max concurrency: 5","timestamp":"2025-06-28T13:33:06.255Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T13:33:06.268Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T13:33:06.268Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-28T13:33:06.268Z"}
{"level":"info","message":"API request received to scrape URL: https://www.ezrankings.com/","timestamp":"2025-06-28T13:33:12.971Z"}
{"level":"info","message":"Starting scrape operation for URL: https://www.ezrankings.com/","timestamp":"2025-06-28T13:33:12.971Z"}
{"level":"info","message":"Initializing Puppeteer cluster...","timestamp":"2025-06-28T13:33:12.972Z"}
{"level":"info","message":"Cluster initialized with max concurrency: 5","timestamp":"2025-06-28T13:33:14.078Z"}
{"level":"info","message":"Executing task for URL: https://www.ezrankings.com","timestamp":"2025-06-28T13:33:14.079Z"}
{"level":"info","message":"Navigation to https://www.ezrankings.com successful","timestamp":"2025-06-28T13:33:19.343Z"}
{"level":"info","message":"Extracting title and description metadata","timestamp":"2025-06-28T13:33:19.344Z"}
{"level":"info","message":"Extracted metadata - Title: Best Digital Marketing Agency ..., Description: Achieve up to 5X conversions w...","timestamp":"2025-06-28T13:33:19.380Z"}
{"level":"info","message":"Extracting links from page","timestamp":"2025-06-28T13:33:19.380Z"}
{"level":"info","message":"Extracted 195 links from the page","timestamp":"2025-06-28T13:33:19.380Z"}
{"level":"info","message":"Converting HTML to text","timestamp":"2025-06-28T13:33:19.381Z"}
{"level":"info","message":"Converted 32256 characters of HTML to text","timestamp":"2025-06-28T13:33:19.406Z"}
{"level":"info","message":"Extracting tables from page","timestamp":"2025-06-28T13:33:19.407Z"}
{"level":"error","message":"Error extracting tables: ReferenceError: __name is not defined","timestamp":"2025-06-28T13:33:19.420Z"}
{"level":"info","message":"Extracting about data from: https://www.ezrankings.com","timestamp":"2025-06-28T13:33:19.421Z"}
{"level":"info","message":"Extracting about page information","timestamp":"2025-06-28T13:33:19.422Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:37:18)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:108:35)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:151:26)","timestamp":"2025-06-28T13:33:19.425Z"}
{"level":"info","message":"Finding about and team pages from: https://www.ezrankings.com","timestamp":"2025-06-28T13:33:19.426Z"}
{"level":"info","message":"Finding about and team pages from: https://www.ezrankings.com","timestamp":"2025-06-28T13:33:19.426Z"}
{"level":"info","message":"No about pages found in DOM, falling back to nested links...","timestamp":"2025-06-28T13:33:19.429Z"}
{"level":"info","message":"Found 1 unique about/team pages from nested links","timestamp":"2025-06-28T13:33:19.430Z"}
{"level":"info","message":"Found 1 about/team pages to extract","timestamp":"2025-06-28T13:33:19.430Z"}
{"level":"info","message":"Extracting company info from 1 pages","timestamp":"2025-06-28T13:33:19.430Z"}
{"level":"info","message":"Extracting about info from: https://www.ezrankings.com/about-us.html","timestamp":"2025-06-28T13:33:21.247Z"}
{"level":"info","message":"Extracting about page information","timestamp":"2025-06-28T13:33:21.247Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:37:18)\n    at async extractCompanyInfoFromSite (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:1189:22)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:120:33)","timestamp":"2025-06-28T13:33:21.248Z"}
{"level":"info","message":"Scrape completed for https://www.ezrankings.com/ in 8292ms","timestamp":"2025-06-28T13:33:21.263Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp\n  - Timeout: 60000ms\n  - Max concurrency: 5","timestamp":"2025-06-28T13:36:27.091Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T13:36:27.103Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T13:36:27.104Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-28T13:36:27.104Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp\n  - Timeout: 60000ms\n  - Max concurrency: 5","timestamp":"2025-06-28T13:39:59.284Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T13:39:59.340Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T13:39:59.340Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-28T13:39:59.340Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp\n  - Timeout: 60000ms\n  - Max concurrency: 5","timestamp":"2025-06-28T13:40:36.883Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T13:40:36.933Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T13:40:36.934Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-28T13:40:36.934Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp\n  - Timeout: 60000ms\n  - Max concurrency: 5","timestamp":"2025-06-28T13:40:42.158Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T13:40:42.195Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T13:40:42.196Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-28T13:40:42.196Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp\n  - Timeout: 60000ms\n  - Max concurrency: 5","timestamp":"2025-06-28T13:41:09.963Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T13:41:10.000Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T13:41:10.001Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-28T13:41:10.001Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp\n  - Timeout: 60000ms\n  - Max concurrency: 5","timestamp":"2025-06-28T13:44:15.740Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T13:44:15.776Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T13:44:15.776Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-28T13:44:15.777Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp\n  - Timeout: 60000ms\n  - Max concurrency: 5","timestamp":"2025-06-28T13:44:37.386Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T13:44:37.419Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T13:44:37.420Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-28T13:44:37.420Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp\n  - Timeout: 60000ms\n  - Max concurrency: 5","timestamp":"2025-06-28T14:07:55.950Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T14:07:55.993Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T14:07:55.994Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-28T14:07:55.994Z"}
{"level":"info","message":"API request received to scrape URL: https://www.ezrankings.com/","timestamp":"2025-06-28T14:08:31.540Z"}
{"level":"info","message":"Starting scrape operation for URL: https://www.ezrankings.com/","timestamp":"2025-06-28T14:08:31.541Z"}
{"level":"info","message":"Initializing Puppeteer cluster...","timestamp":"2025-06-28T14:08:31.542Z"}
{"level":"info","message":"Cluster initialized with max concurrency: 5","timestamp":"2025-06-28T14:08:32.486Z"}
{"level":"info","message":"Executing task for URL: https://www.ezrankings.com","timestamp":"2025-06-28T14:08:32.486Z"}
{"level":"info","message":"Navigation to https://www.ezrankings.com successful","timestamp":"2025-06-28T14:08:36.626Z"}
{"level":"info","message":"Extracting title and description metadata","timestamp":"2025-06-28T14:08:36.627Z"}
{"level":"info","message":"Extracted metadata - Title: Best Digital Marketing Agency ..., Description: Achieve up to 5X conversions w...","timestamp":"2025-06-28T14:08:36.643Z"}
{"level":"info","message":"Extracting links from page","timestamp":"2025-06-28T14:08:36.644Z"}
{"level":"info","message":"Extracted 195 links from the page","timestamp":"2025-06-28T14:08:36.646Z"}
{"level":"info","message":"Converting HTML to text","timestamp":"2025-06-28T14:08:36.647Z"}
{"level":"info","message":"Converted 32256 characters of HTML to text","timestamp":"2025-06-28T14:08:36.678Z"}
{"level":"info","message":"Extracting tables from page","timestamp":"2025-06-28T14:08:36.678Z"}
{"level":"error","message":"Error extracting tables: ReferenceError: __name is not defined","timestamp":"2025-06-28T14:08:36.699Z"}
{"level":"info","message":"Extracting about data from: https://www.ezrankings.com","timestamp":"2025-06-28T14:08:36.700Z"}
{"level":"info","message":"Extracting about page information","timestamp":"2025-06-28T14:08:36.700Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:37:18)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:108:35)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:151:26)","timestamp":"2025-06-28T14:08:36.705Z"}
{"level":"info","message":"Finding about and team pages from: https://www.ezrankings.com","timestamp":"2025-06-28T14:08:36.705Z"}
{"level":"info","message":"Finding about and team pages from: https://www.ezrankings.com","timestamp":"2025-06-28T14:08:36.706Z"}
{"level":"info","message":"No about pages found in DOM, falling back to nested links...","timestamp":"2025-06-28T14:08:36.708Z"}
{"level":"info","message":"Found 1 unique about/team pages from nested links","timestamp":"2025-06-28T14:08:36.709Z"}
{"level":"info","message":"Found 1 about/team pages to extract","timestamp":"2025-06-28T14:08:36.709Z"}
{"level":"info","message":"Extracting company info from 1 pages","timestamp":"2025-06-28T14:08:36.709Z"}
{"level":"info","message":"Extracting about info from: https://www.ezrankings.com/about-us.html","timestamp":"2025-06-28T14:08:37.810Z"}
{"level":"info","message":"Extracting about page information","timestamp":"2025-06-28T14:08:37.810Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:37:18)\n    at async extractCompanyInfoFromSite (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:1189:22)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:120:33)","timestamp":"2025-06-28T14:08:37.820Z"}
{"level":"info","message":"Scrape completed for https://www.ezrankings.com/ in 6296ms","timestamp":"2025-06-28T14:08:37.837Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp\n  - Timeout: 60000ms\n  - Max concurrency: 5","timestamp":"2025-06-28T14:11:58.849Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T14:11:58.861Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T14:11:58.861Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-28T14:11:58.861Z"}
{"level":"info","message":"API request received to scrape URL: https://www.ezrankings.com/","timestamp":"2025-06-28T14:16:06.280Z"}
{"level":"info","message":"Starting scrape operation for URL: https://www.ezrankings.com/","timestamp":"2025-06-28T14:16:06.280Z"}
{"level":"info","message":"Initializing Puppeteer cluster...","timestamp":"2025-06-28T14:16:06.281Z"}
{"level":"info","message":"Cluster initialized with max concurrency: 5","timestamp":"2025-06-28T14:16:07.176Z"}
{"level":"info","message":"Executing task for URL: https://www.ezrankings.com","timestamp":"2025-06-28T14:16:07.176Z"}
{"level":"info","message":"Navigation to https://www.ezrankings.com successful","timestamp":"2025-06-28T14:16:10.291Z"}
{"level":"info","message":"Extracting title and description metadata","timestamp":"2025-06-28T14:16:10.292Z"}
{"level":"info","message":"Extracted metadata - Title: Best Digital Marketing Agency ..., Description: Achieve up to 5X conversions w...","timestamp":"2025-06-28T14:16:10.300Z"}
{"level":"info","message":"Extracting links from page","timestamp":"2025-06-28T14:16:10.300Z"}
{"level":"info","message":"Extracted 195 links from the page","timestamp":"2025-06-28T14:16:10.304Z"}
{"level":"info","message":"Converting HTML to text","timestamp":"2025-06-28T14:16:10.305Z"}
{"level":"info","message":"Converted 32256 characters of HTML to text","timestamp":"2025-06-28T14:16:10.335Z"}
{"level":"info","message":"Extracting tables from page","timestamp":"2025-06-28T14:16:10.335Z"}
{"level":"error","message":"Error extracting tables: ReferenceError: __name is not defined","timestamp":"2025-06-28T14:16:10.357Z"}
{"level":"info","message":"Extracting about data from: https://www.ezrankings.com","timestamp":"2025-06-28T14:16:10.358Z"}
{"level":"info","message":"Extracting about page information","timestamp":"2025-06-28T14:16:10.358Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:37:18)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:108:35)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:151:26)","timestamp":"2025-06-28T14:16:10.361Z"}
{"level":"info","message":"Finding about and team pages from: https://www.ezrankings.com","timestamp":"2025-06-28T14:16:10.362Z"}
{"level":"info","message":"Finding about and team pages from: https://www.ezrankings.com","timestamp":"2025-06-28T14:16:10.362Z"}
{"level":"info","message":"No about pages found in DOM, falling back to nested links...","timestamp":"2025-06-28T14:16:10.364Z"}
{"level":"info","message":"Found 1 unique about/team pages from nested links","timestamp":"2025-06-28T14:16:10.364Z"}
{"level":"info","message":"Found 1 about/team pages to extract","timestamp":"2025-06-28T14:16:10.364Z"}
{"level":"info","message":"Extracting company info from 1 pages","timestamp":"2025-06-28T14:16:10.364Z"}
{"level":"info","message":"Extracting about info from: https://www.ezrankings.com/about-us.html","timestamp":"2025-06-28T14:16:11.507Z"}
{"level":"info","message":"Extracting about page information","timestamp":"2025-06-28T14:16:11.507Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:37:18)\n    at async extractCompanyInfoFromSite (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:1189:22)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:120:33)","timestamp":"2025-06-28T14:16:11.519Z"}
{"level":"info","message":"Scrape completed for https://www.ezrankings.com/ in 5253ms","timestamp":"2025-06-28T14:16:11.533Z"}
{"level":"info","message":"API request received to scrape URL: https://www.ezrankings.com/","timestamp":"2025-06-28T14:23:57.754Z"}
{"level":"info","message":"Starting scrape operation for URL: https://www.ezrankings.com/","timestamp":"2025-06-28T14:23:57.757Z"}
{"level":"info","message":"Executing task for URL: https://www.ezrankings.com","timestamp":"2025-06-28T14:23:57.758Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp\n  - Timeout: 60000ms\n  - Max concurrency: 5","timestamp":"2025-06-28T14:24:38.605Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T14:24:38.630Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T14:24:38.630Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-28T14:24:38.630Z"}
{"level":"info","message":"API request received to scrape URL: https://www.ezrankings.com/","timestamp":"2025-06-28T14:24:43.398Z"}
{"level":"info","message":"Starting scrape operation for URL: https://www.ezrankings.com/","timestamp":"2025-06-28T14:24:43.398Z"}
{"level":"info","message":"Initializing Puppeteer cluster...","timestamp":"2025-06-28T14:24:43.398Z"}
{"level":"info","message":"Cluster initialized with max concurrency: 5","timestamp":"2025-06-28T14:24:44.252Z"}
{"level":"info","message":"Executing task for URL: https://www.ezrankings.com","timestamp":"2025-06-28T14:24:44.253Z"}
{"level":"info","message":"Navigation to https://www.ezrankings.com successful","timestamp":"2025-06-28T14:24:48.666Z"}
{"level":"info","message":"Extracting title and description metadata","timestamp":"2025-06-28T14:24:48.667Z"}
{"level":"info","message":"Extracted metadata - Title: Best Digital Marketing Agency ..., Description: Achieve up to 5X conversions w...","timestamp":"2025-06-28T14:24:48.672Z"}
{"level":"info","message":"Extracting links from page","timestamp":"2025-06-28T14:24:48.674Z"}
{"level":"info","message":"Extracted 195 links from the page","timestamp":"2025-06-28T14:24:48.676Z"}
{"level":"info","message":"Converting HTML to text","timestamp":"2025-06-28T14:24:48.677Z"}
{"level":"info","message":"Converted 32256 characters of HTML to text","timestamp":"2025-06-28T14:24:48.709Z"}
{"level":"info","message":"Extracting tables from page","timestamp":"2025-06-28T14:24:48.709Z"}
{"level":"error","message":"Error extracting tables: ReferenceError: __name is not defined","timestamp":"2025-06-28T14:24:48.720Z"}
{"level":"info","message":"Extracting about data from: https://www.ezrankings.com","timestamp":"2025-06-28T14:24:48.721Z"}
{"level":"info","message":"Extracting about page information","timestamp":"2025-06-28T14:24:48.721Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:37:18)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:108:35)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:151:26)","timestamp":"2025-06-28T14:24:48.723Z"}
{"level":"info","message":"Finding about and team pages from: https://www.ezrankings.com","timestamp":"2025-06-28T14:24:48.723Z"}
{"level":"info","message":"Finding about and team pages from: https://www.ezrankings.com","timestamp":"2025-06-28T14:24:48.723Z"}
{"level":"info","message":"No about pages found in DOM, falling back to nested links...","timestamp":"2025-06-28T14:24:48.725Z"}
{"level":"info","message":"Found 1 unique about/team pages from nested links","timestamp":"2025-06-28T14:24:48.725Z"}
{"level":"info","message":"Found 1 about/team pages to extract","timestamp":"2025-06-28T14:24:48.725Z"}
{"level":"info","message":"Extracting company info from 1 pages","timestamp":"2025-06-28T14:24:48.725Z"}
{"level":"info","message":"Extracting about info from: https://www.ezrankings.com/about-us.html","timestamp":"2025-06-28T14:24:50.024Z"}
{"level":"info","message":"Extracting about page information","timestamp":"2025-06-28T14:24:50.024Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:37:18)\n    at async extractCompanyInfoFromSite (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:1189:22)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:120:33)","timestamp":"2025-06-28T14:24:50.028Z"}
{"level":"info","message":"Scrape completed for https://www.ezrankings.com/ in 6652ms","timestamp":"2025-06-28T14:24:50.050Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp\n  - Timeout: 60000ms\n  - Max concurrency: 5","timestamp":"2025-06-28T14:30:36.867Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T14:30:36.881Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T14:30:36.881Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-28T14:30:36.882Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp\n  - Timeout: 60000ms\n  - Max concurrency: 5","timestamp":"2025-06-28T14:30:50.511Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T14:30:50.550Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T14:30:50.555Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-28T14:30:50.555Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp\n  - Timeout: 60000ms\n  - Max concurrency: 5","timestamp":"2025-06-28T14:31:05.375Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T14:31:05.416Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T14:31:05.416Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-28T14:31:05.417Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp\n  - Timeout: 60000ms\n  - Max concurrency: 5","timestamp":"2025-06-28T14:31:20.392Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T14:31:20.425Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T14:31:20.426Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-28T14:31:20.426Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp\n  - Timeout: 60000ms\n  - Max concurrency: 5","timestamp":"2025-06-28T14:31:35.119Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T14:31:35.153Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T14:31:35.154Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-28T14:31:35.154Z"}
{"level":"info","message":"API request received to scrape URL: https://www.ezrankings.com/","timestamp":"2025-06-28T14:31:47.554Z"}
{"level":"info","message":"Starting scrape operation for URL: https://www.ezrankings.com/","timestamp":"2025-06-28T14:31:47.554Z"}
{"level":"info","message":"Initializing Puppeteer cluster...","timestamp":"2025-06-28T14:31:47.555Z"}
{"level":"info","message":"Cluster initialized with max concurrency: 5","timestamp":"2025-06-28T14:31:48.444Z"}
{"level":"info","message":"Executing task for URL: https://www.ezrankings.com","timestamp":"2025-06-28T14:31:48.444Z"}
{"level":"info","message":"Navigation to https://www.ezrankings.com successful","timestamp":"2025-06-28T14:31:54.923Z"}
{"level":"info","message":"Extracting title and description metadata","timestamp":"2025-06-28T14:31:54.923Z"}
{"level":"info","message":"Extracted metadata - Title: Best Digital Marketing Agency ..., Description: Achieve up to 5X conversions w...","timestamp":"2025-06-28T14:31:54.929Z"}
{"level":"info","message":"Extracting links from page","timestamp":"2025-06-28T14:31:54.930Z"}
{"level":"info","message":"Extracted 195 links from the page","timestamp":"2025-06-28T14:31:54.938Z"}
{"level":"info","message":"Converting HTML to text","timestamp":"2025-06-28T14:31:54.938Z"}
{"level":"info","message":"Converted 32256 characters of HTML to text","timestamp":"2025-06-28T14:31:54.976Z"}
{"level":"info","message":"Extracting tables from page","timestamp":"2025-06-28T14:31:54.977Z"}
{"level":"error","message":"Error extracting tables: ReferenceError: __name is not defined","timestamp":"2025-06-28T14:31:55.001Z"}
{"level":"info","message":"Extracting about data from: https://www.ezrankings.com","timestamp":"2025-06-28T14:31:55.001Z"}
{"level":"info","message":"Extracting about page information","timestamp":"2025-06-28T14:31:55.002Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:37:18)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:108:35)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:151:26)","timestamp":"2025-06-28T14:31:55.005Z"}
{"level":"info","message":"Finding about and team pages from: https://www.ezrankings.com","timestamp":"2025-06-28T14:31:55.006Z"}
{"level":"info","message":"Finding about and team pages from: https://www.ezrankings.com","timestamp":"2025-06-28T14:31:55.006Z"}
{"level":"info","message":"No about pages found in DOM, falling back to nested links...","timestamp":"2025-06-28T14:31:55.007Z"}
{"level":"info","message":"Found 1 unique about/team pages from nested links","timestamp":"2025-06-28T14:31:55.008Z"}
{"level":"info","message":"Found 1 about/team pages to extract","timestamp":"2025-06-28T14:31:55.008Z"}
{"level":"info","message":"Extracting company info from 1 pages","timestamp":"2025-06-28T14:31:55.008Z"}
{"level":"info","message":"Extracting about info from: https://www.ezrankings.com/about-us.html","timestamp":"2025-06-28T14:31:56.262Z"}
{"level":"info","message":"Extracting about page information","timestamp":"2025-06-28T14:31:56.262Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:37:18)\n    at async extractCompanyInfoFromSite (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:1189:22)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:120:33)","timestamp":"2025-06-28T14:31:56.264Z"}
{"level":"info","message":"Scrape completed for https://www.ezrankings.com/ in 8758ms","timestamp":"2025-06-28T14:31:56.312Z"}
{"level":"info","message":"API request received to scrape URL: https://www.ezrankings.com/","timestamp":"2025-06-28T14:32:55.834Z"}
{"level":"info","message":"Starting scrape operation for URL: https://www.ezrankings.com/","timestamp":"2025-06-28T14:32:55.835Z"}
{"level":"info","message":"Executing task for URL: https://www.ezrankings.com","timestamp":"2025-06-28T14:32:55.836Z"}
{"level":"info","message":"Navigation to https://www.ezrankings.com successful","timestamp":"2025-06-28T14:33:01.710Z"}
{"level":"info","message":"Extracting title and description metadata","timestamp":"2025-06-28T14:33:01.711Z"}
{"level":"info","message":"Extracted metadata - Title: Best Digital Marketing Agency ..., Description: Achieve up to 5X conversions w...","timestamp":"2025-06-28T14:33:01.750Z"}
{"level":"info","message":"Extracting links from page","timestamp":"2025-06-28T14:33:01.750Z"}
{"level":"info","message":"Extracted 195 links from the page","timestamp":"2025-06-28T14:33:01.751Z"}
{"level":"info","message":"Converting HTML to text","timestamp":"2025-06-28T14:33:01.751Z"}
{"level":"info","message":"Converted 32256 characters of HTML to text","timestamp":"2025-06-28T14:33:01.775Z"}
{"level":"info","message":"Extracting tables from page","timestamp":"2025-06-28T14:33:01.776Z"}
{"level":"error","message":"Error extracting tables: ReferenceError: __name is not defined","timestamp":"2025-06-28T14:33:01.777Z"}
{"level":"info","message":"Extracting about data from: https://www.ezrankings.com","timestamp":"2025-06-28T14:33:01.778Z"}
{"level":"info","message":"Extracting about page information","timestamp":"2025-06-28T14:33:01.778Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:37:18)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:108:35)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:151:26)","timestamp":"2025-06-28T14:33:01.779Z"}
{"level":"info","message":"Finding about and team pages from: https://www.ezrankings.com","timestamp":"2025-06-28T14:33:01.779Z"}
{"level":"info","message":"Finding about and team pages from: https://www.ezrankings.com","timestamp":"2025-06-28T14:33:01.779Z"}
{"level":"info","message":"No about pages found in DOM, falling back to nested links...","timestamp":"2025-06-28T14:33:01.780Z"}
{"level":"info","message":"Found 1 unique about/team pages from nested links","timestamp":"2025-06-28T14:33:01.780Z"}
{"level":"info","message":"Found 1 about/team pages to extract","timestamp":"2025-06-28T14:33:01.780Z"}
{"level":"info","message":"Extracting company info from 1 pages","timestamp":"2025-06-28T14:33:01.780Z"}
{"level":"info","message":"Extracting about info from: https://www.ezrankings.com/about-us.html","timestamp":"2025-06-28T14:33:03.155Z"}
{"level":"info","message":"Extracting about page information","timestamp":"2025-06-28T14:33:03.157Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:37:18)\n    at async extractCompanyInfoFromSite (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:1189:22)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:120:33)","timestamp":"2025-06-28T14:33:03.161Z"}
{"level":"info","message":"Scrape completed for https://www.ezrankings.com/ in 7343ms","timestamp":"2025-06-28T14:33:03.178Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp\n  - Timeout: 60000ms\n  - Max concurrency: 5","timestamp":"2025-06-28T14:35:04.572Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T14:35:04.607Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T14:35:04.608Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-28T14:35:04.608Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp\n  - Timeout: 60000ms\n  - Max concurrency: 5","timestamp":"2025-06-28T14:35:51.404Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T14:35:51.446Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T14:35:51.447Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-28T14:35:51.447Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp\n  - Timeout: 60000ms\n  - Max concurrency: 5","timestamp":"2025-06-28T14:36:19.081Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T14:36:19.128Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T14:36:19.131Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-28T14:36:19.131Z"}
{"level":"info","message":"API request received to scrape URL: https://www.ezrankings.com/","timestamp":"2025-06-28T14:36:28.956Z"}
{"level":"info","message":"Starting scrape operation for URL: https://www.ezrankings.com/","timestamp":"2025-06-28T14:36:28.957Z"}
{"level":"info","message":"Initializing Puppeteer cluster...","timestamp":"2025-06-28T14:36:28.958Z"}
{"level":"info","message":"Cluster initialized with max concurrency: 5","timestamp":"2025-06-28T14:36:29.841Z"}
{"level":"info","message":"Executing task for URL: https://www.ezrankings.com","timestamp":"2025-06-28T14:36:29.841Z"}
{"level":"info","message":"Navigation to https://www.ezrankings.com successful","timestamp":"2025-06-28T14:36:33.520Z"}
{"level":"info","message":"Extracting title and description metadata","timestamp":"2025-06-28T14:36:33.521Z"}
{"level":"info","message":"Extracted metadata - Title: Best Digital Marketing Agency ..., Description: Achieve up to 5X conversions w...","timestamp":"2025-06-28T14:36:33.525Z"}
{"level":"info","message":"Extracting links from page","timestamp":"2025-06-28T14:36:33.526Z"}
{"level":"info","message":"Extracted 195 links from the page","timestamp":"2025-06-28T14:36:33.531Z"}
{"level":"info","message":"Converting HTML to text","timestamp":"2025-06-28T14:36:33.531Z"}
{"level":"info","message":"Converted 32256 characters of HTML to text","timestamp":"2025-06-28T14:36:33.561Z"}
{"level":"info","message":"Extracting tables from page","timestamp":"2025-06-28T14:36:33.562Z"}
{"level":"info","message":"Extracted 0 tables from the page","timestamp":"2025-06-28T14:36:33.563Z"}
{"level":"info","message":"Extracting about data from: https://www.ezrankings.com","timestamp":"2025-06-28T14:36:33.564Z"}
{"level":"info","message":"Extracting about page information","timestamp":"2025-06-28T14:36:33.565Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:37:18)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:108:35)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:151:26)","timestamp":"2025-06-28T14:36:33.581Z"}
{"level":"info","message":"Finding about and team pages from: https://www.ezrankings.com","timestamp":"2025-06-28T14:36:33.581Z"}
{"level":"info","message":"Finding about and team pages from: https://www.ezrankings.com","timestamp":"2025-06-28T14:36:33.581Z"}
{"level":"info","message":"No about pages found in DOM, falling back to nested links...","timestamp":"2025-06-28T14:36:33.585Z"}
{"level":"info","message":"Found 1 unique about/team pages from nested links","timestamp":"2025-06-28T14:36:33.585Z"}
{"level":"info","message":"Found 1 about/team pages to extract","timestamp":"2025-06-28T14:36:33.585Z"}
{"level":"info","message":"Extracting company info from 1 pages","timestamp":"2025-06-28T14:36:33.585Z"}
{"level":"info","message":"Extracting about info from: https://www.ezrankings.com/about-us.html","timestamp":"2025-06-28T14:36:35.182Z"}
{"level":"info","message":"Extracting about page information","timestamp":"2025-06-28T14:36:35.183Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:37:18)\n    at async extractCompanyInfoFromSite (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:1189:22)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:120:33)","timestamp":"2025-06-28T14:36:35.187Z"}
{"level":"info","message":"Scrape completed for https://www.ezrankings.com/ in 6249ms","timestamp":"2025-06-28T14:36:35.206Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp\n  - Timeout: 60000ms\n  - Max concurrency: 5","timestamp":"2025-06-28T14:38:23.940Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T14:38:23.954Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T14:38:23.954Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-28T14:38:23.954Z"}
{"level":"info","message":"API request received to scrape URL: https://www.ezrankings.com/","timestamp":"2025-06-28T14:38:27.607Z"}
{"level":"info","message":"Starting scrape operation for URL: https://www.ezrankings.com/","timestamp":"2025-06-28T14:38:27.608Z"}
{"level":"info","message":"Initializing Puppeteer cluster...","timestamp":"2025-06-28T14:38:27.611Z"}
{"level":"info","message":"Cluster initialized with max concurrency: 5","timestamp":"2025-06-28T14:38:28.205Z"}
{"level":"info","message":"Executing task for URL: https://www.ezrankings.com","timestamp":"2025-06-28T14:38:28.206Z"}
{"level":"info","message":"Navigation to https://www.ezrankings.com successful","timestamp":"2025-06-28T14:38:34.348Z"}
{"level":"info","message":"Extracting title and description metadata","timestamp":"2025-06-28T14:38:34.349Z"}
{"level":"info","message":"Extracted metadata - Title: Best Digital Marketing Agency ..., Description: Achieve up to 5X conversions w...","timestamp":"2025-06-28T14:38:34.352Z"}
{"level":"info","message":"Extracting links from page","timestamp":"2025-06-28T14:38:34.352Z"}
{"level":"info","message":"Extracted 195 links from the page","timestamp":"2025-06-28T14:38:34.353Z"}
{"level":"info","message":"Converting HTML to text","timestamp":"2025-06-28T14:38:34.354Z"}
{"level":"info","message":"Converted 32256 characters of HTML to text","timestamp":"2025-06-28T14:38:34.398Z"}
{"level":"info","message":"Extracting tables from page","timestamp":"2025-06-28T14:38:34.398Z"}
{"level":"info","message":"Extracted 0 tables from the page","timestamp":"2025-06-28T14:38:34.399Z"}
{"level":"info","message":"Extracting about data from: https://www.ezrankings.com","timestamp":"2025-06-28T14:38:34.400Z"}
{"level":"info","message":"Extracting about page information","timestamp":"2025-06-28T14:38:34.400Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:36:18)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:108:35)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:151:26)","timestamp":"2025-06-28T14:38:34.425Z"}
{"level":"info","message":"Finding about and team pages from: https://www.ezrankings.com","timestamp":"2025-06-28T14:38:34.426Z"}
{"level":"info","message":"Finding about and team pages from: https://www.ezrankings.com","timestamp":"2025-06-28T14:38:34.426Z"}
{"level":"info","message":"No about pages found in DOM, falling back to nested links...","timestamp":"2025-06-28T14:38:34.428Z"}
{"level":"info","message":"Found 1 unique about/team pages from nested links","timestamp":"2025-06-28T14:38:34.429Z"}
{"level":"info","message":"Found 1 about/team pages to extract","timestamp":"2025-06-28T14:38:34.429Z"}
{"level":"info","message":"Extracting company info from 1 pages","timestamp":"2025-06-28T14:38:34.429Z"}
{"level":"info","message":"Extracting about info from: https://www.ezrankings.com/about-us.html","timestamp":"2025-06-28T14:38:36.017Z"}
{"level":"info","message":"Extracting about page information","timestamp":"2025-06-28T14:38:36.018Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:36:18)\n    at async extractCompanyInfoFromSite (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:1188:22)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:120:33)","timestamp":"2025-06-28T14:38:36.021Z"}
{"level":"info","message":"Scrape completed for https://www.ezrankings.com/ in 8428ms","timestamp":"2025-06-28T14:38:36.036Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp\n  - Timeout: 60000ms\n  - Max concurrency: 5","timestamp":"2025-06-28T14:40:25.242Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T14:40:25.254Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T14:40:25.254Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-28T14:40:25.254Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp\n  - Timeout: 60000ms\n  - Max concurrency: 5","timestamp":"2025-06-28T14:41:06.894Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T14:41:06.934Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T14:41:06.934Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-28T14:41:06.934Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp\n  - Timeout: 60000ms\n  - Max concurrency: 5","timestamp":"2025-06-28T14:41:27.855Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T14:41:27.893Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T14:41:27.894Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-28T14:41:27.894Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp\n  - Timeout: 60000ms\n  - Max concurrency: 5","timestamp":"2025-06-28T14:42:03.712Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T14:42:03.815Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T14:42:03.817Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-28T14:42:03.817Z"}
{"level":"info","message":"API request received to scrape URL: https://www.ezrankings.com/","timestamp":"2025-06-28T14:42:25.986Z"}
{"level":"info","message":"Starting scrape operation for URL: https://www.ezrankings.com/","timestamp":"2025-06-28T14:42:25.986Z"}
{"level":"info","message":"Initializing Puppeteer cluster...","timestamp":"2025-06-28T14:42:25.987Z"}
{"level":"info","message":"Cluster initialized with max concurrency: 5","timestamp":"2025-06-28T14:42:26.912Z"}
{"level":"info","message":"Executing task for URL: https://www.ezrankings.com","timestamp":"2025-06-28T14:42:26.913Z"}
{"level":"info","message":"Navigation to https://www.ezrankings.com successful","timestamp":"2025-06-28T14:42:30.191Z"}
{"level":"info","message":"Extracting title and description metadata","timestamp":"2025-06-28T14:42:30.191Z"}
{"level":"info","message":"Extracted metadata - Title: Best Digital Marketing Agency ..., Description: Achieve up to 5X conversions w...","timestamp":"2025-06-28T14:42:30.192Z"}
{"level":"info","message":"Extracting links from page","timestamp":"2025-06-28T14:42:30.192Z"}
{"level":"info","message":"Extracted 195 links from the page","timestamp":"2025-06-28T14:42:30.194Z"}
{"level":"info","message":"Converting HTML to text","timestamp":"2025-06-28T14:42:30.194Z"}
{"level":"info","message":"Converted 32256 characters of HTML to text","timestamp":"2025-06-28T14:42:30.221Z"}
{"level":"info","message":"Extracting tables from page","timestamp":"2025-06-28T14:42:30.222Z"}
{"level":"info","message":"Extracted 0 tables from the page","timestamp":"2025-06-28T14:42:30.222Z"}
{"level":"info","message":"Extracting about data from: https://www.ezrankings.com","timestamp":"2025-06-28T14:42:30.223Z"}
{"level":"info","message":"Extracting about page information","timestamp":"2025-06-28T14:42:30.223Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:36:18)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:108:35)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:151:26)","timestamp":"2025-06-28T14:42:30.233Z"}
{"level":"info","message":"Finding about and team pages from: https://www.ezrankings.com","timestamp":"2025-06-28T14:42:30.234Z"}
{"level":"info","message":"Finding about and team pages from: https://www.ezrankings.com","timestamp":"2025-06-28T14:42:30.234Z"}
{"level":"info","message":"No about pages found in DOM, falling back to nested links...","timestamp":"2025-06-28T14:42:30.235Z"}
{"level":"info","message":"Found 1 unique about/team pages from nested links","timestamp":"2025-06-28T14:42:30.236Z"}
{"level":"info","message":"Found 1 about/team pages to extract","timestamp":"2025-06-28T14:42:30.236Z"}
{"level":"info","message":"Extracting company info from 1 pages","timestamp":"2025-06-28T14:42:30.236Z"}
{"level":"info","message":"Extracting about info from: https://www.ezrankings.com/about-us.html","timestamp":"2025-06-28T14:42:31.483Z"}
{"level":"info","message":"Extracting about page information","timestamp":"2025-06-28T14:42:31.483Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:36:18)\n    at async extractCompanyInfoFromSite (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:1189:22)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:120:33)","timestamp":"2025-06-28T14:42:31.486Z"}
{"level":"info","message":"Scrape completed for https://www.ezrankings.com/ in 5519ms","timestamp":"2025-06-28T14:42:31.505Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp\n  - Timeout: 60000ms\n  - Max concurrency: 5","timestamp":"2025-06-28T14:45:49.400Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T14:45:49.414Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T14:45:49.414Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-28T14:45:49.414Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp\n  - Timeout: 60000ms\n  - Max concurrency: 5","timestamp":"2025-06-28T14:46:00.894Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T14:46:00.930Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T14:46:00.930Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-28T14:46:00.930Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp\n  - Timeout: 60000ms\n  - Max concurrency: 5","timestamp":"2025-06-28T14:46:13.503Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T14:46:13.537Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T14:46:13.538Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-28T14:46:13.539Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp\n  - Timeout: 60000ms\n  - Max concurrency: 5","timestamp":"2025-06-28T14:46:34.851Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T14:46:34.890Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T14:46:34.890Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-28T14:46:34.891Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp\n  - Timeout: 60000ms\n  - Max concurrency: 5","timestamp":"2025-06-28T14:46:50.500Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T14:46:50.612Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T14:46:50.613Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-28T14:46:50.613Z"}
{"level":"info","message":"API request received to scrape URL: https://www.ezrankings.com/","timestamp":"2025-06-28T14:47:01.325Z"}
{"level":"info","message":"Starting scrape operation for URL: https://www.ezrankings.com/","timestamp":"2025-06-28T14:47:01.326Z"}
{"level":"info","message":"Initializing Puppeteer cluster...","timestamp":"2025-06-28T14:47:01.327Z"}
{"level":"info","message":"Cluster initialized with max concurrency: 5","timestamp":"2025-06-28T14:47:02.275Z"}
{"level":"info","message":"Executing task for URL: https://www.ezrankings.com","timestamp":"2025-06-28T14:47:02.275Z"}
{"level":"info","message":"Navigation to https://www.ezrankings.com successful","timestamp":"2025-06-28T14:47:05.766Z"}
{"level":"info","message":"Extracting title and description metadata","timestamp":"2025-06-28T14:47:05.767Z"}
{"level":"info","message":"Extracted metadata - Title: Best Digital Marketing Agency ..., Description: Achieve up to 5X conversions w...","timestamp":"2025-06-28T14:47:05.769Z"}
{"level":"info","message":"Extracting links from page","timestamp":"2025-06-28T14:47:05.770Z"}
{"level":"info","message":"Extracted 195 links from the page","timestamp":"2025-06-28T14:47:05.775Z"}
{"level":"info","message":"Converting HTML to text","timestamp":"2025-06-28T14:47:05.776Z"}
{"level":"info","message":"Converted 32256 characters of HTML to text","timestamp":"2025-06-28T14:47:05.801Z"}
{"level":"info","message":"Extracting tables from page","timestamp":"2025-06-28T14:47:05.801Z"}
{"level":"info","message":"Extracted 0 tables from the page","timestamp":"2025-06-28T14:47:05.802Z"}
{"level":"info","message":"Extracting about data from: https://www.ezrankings.com","timestamp":"2025-06-28T14:47:05.803Z"}
{"level":"info","message":"Extracting about page information","timestamp":"2025-06-28T14:47:05.803Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:36:18)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:108:35)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:151:26)","timestamp":"2025-06-28T14:47:05.815Z"}
{"level":"info","message":"Finding about and team pages from: https://www.ezrankings.com","timestamp":"2025-06-28T14:47:05.815Z"}
{"level":"info","message":"Finding about and team pages from: https://www.ezrankings.com","timestamp":"2025-06-28T14:47:05.815Z"}
{"level":"info","message":"No about pages found in DOM, falling back to nested links...","timestamp":"2025-06-28T14:47:05.817Z"}
{"level":"info","message":"Found 1 unique about/team pages from nested links","timestamp":"2025-06-28T14:47:05.818Z"}
{"level":"info","message":"Found 1 about/team pages to extract","timestamp":"2025-06-28T14:47:05.818Z"}
{"level":"info","message":"Extracting company info from 1 pages","timestamp":"2025-06-28T14:47:05.818Z"}
{"level":"info","message":"Extracting about info from: https://www.ezrankings.com/about-us.html","timestamp":"2025-06-28T14:47:06.922Z"}
{"level":"info","message":"Extracting about page information","timestamp":"2025-06-28T14:47:06.922Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:36:18)\n    at async extractCompanyInfoFromSite (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:1189:22)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:120:33)","timestamp":"2025-06-28T14:47:06.924Z"}
{"level":"info","message":"Scrape completed for https://www.ezrankings.com/ in 5614ms","timestamp":"2025-06-28T14:47:06.940Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp\n  - Timeout: 60000ms\n  - Max concurrency: 5","timestamp":"2025-06-28T14:50:06.843Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T14:50:06.867Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T14:50:06.867Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-28T14:50:06.867Z"}
