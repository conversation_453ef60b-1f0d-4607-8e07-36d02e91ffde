/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/no-unsafe-function-type */
import logger from './logger';

export class ScraperError extends Error {
	constructor(
		message: string,
		public code: string = "SCRAPER_ERROR",
		public originalError?: Error,
	) {
		super(message);
		this.name = "ScraperError";

		// Maintain proper stack trace
		if (Error.captureStackTrace) {
			Error.captureStackTrace(this, ScraperError);
		}
	}
}

export function handleError(err: Error, context = ""): never {
	const error =
		err instanceof ScraperError
			? err
			: new ScraperError(
					`Error in ${context}: ${err.message}`,
					"UNKNOWN_ERROR",
					err,
				);

	logger.error({
		message: error.message,
		code: error.code,
		stack: error.stack,
		context,
	});

	throw error;
}

export function catchAndLog(fn: Function, context = "") {
	return async (...args: any[]) => {
		try {
			return await fn(...args);
		} catch (error) {
			handleError(error as Error, context);
		}
	};
}
