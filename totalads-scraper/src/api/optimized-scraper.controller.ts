/**
 * Optimized scraper API controller with performance improvements
 */

import { Request, Response } from 'express';
import { OptimizedScraperService } from '../core/optimized-scraper.service';
import { performanceMonitor } from '../utils/performance-monitor';
import logger from '../utils/logger';
import { handleError } from '../utils/error-handler';

/**
 * Controller for optimized scraping operations
 */
export class OptimizedScraperController {
  private scraperService: OptimizedScraperService;

  constructor() {
    this.scraperService = new OptimizedScraperService();
    logger.info("Optimized scraper controller initialized");
  }

  /**
   * Scrape a single URL with optimizations
   */
  async scrapeUrl(req: Request, res: Response): Promise<void> {
    const operationId = performanceMonitor.startOperation('scrape_url');
    
    try {
      const { url } = req.body;
      
      if (!url) {
        res.status(400).json({
          status: 400,
          message: "URL is required",
          payload: {
            success: false,
            error: "Missing URL parameter"
          }
        });
        return;
      }

      logger.info(`Optimized scrape request for: ${url}`);

      // Validate URL format
      try {
        new URL(url);
      } catch (urlError) {
        res.status(400).json({
          status: 400,
          message: "Invalid URL format",
          payload: {
            success: false,
            error: "Invalid URL format"
          }
        });
        return;
      }

      // Execute optimized scraping
      const result = await performanceMonitor.timeOperation(
        'scrape_execution',
        () => this.scraperService.scrape({ url }),
        { url }
      );

      performanceMonitor.stopOperation(operationId, { 
        url, 
        success: true,
        dataSize: JSON.stringify(result).length
      });

      res.status(200).json({
        status: 200,
        message: "OK",
        payload: {
          success: true,
          data: result
        }
      });

    } catch (error) {
      performanceMonitor.stopOperation(operationId, { 
        success: false, 
        error: (error as Error).message 
      });

      logger.error("Error in optimized scrape:", error);
      
      res.status(500).json({
        status: 500,
        message: "Internal server error",
        payload: {
          success: false,
          error: (error as Error).message
        }
      });
    }
  }

  /**
   * Scrape multiple URLs in batch with concurrency control
   */
  async scrapeBatch(req: Request, res: Response): Promise<void> {
    const operationId = performanceMonitor.startOperation('scrape_batch');
    
    try {
      const { urls } = req.body;
      
      if (!urls || !Array.isArray(urls) || urls.length === 0) {
        res.status(400).json({
          status: 400,
          message: "URLs array is required",
          payload: {
            success: false,
            error: "Missing or invalid URLs parameter"
          }
        });
        return;
      }

      // Limit batch size for performance
      if (urls.length > 10) {
        res.status(400).json({
          status: 400,
          message: "Batch size too large",
          payload: {
            success: false,
            error: "Maximum 10 URLs allowed per batch"
          }
        });
        return;
      }

      logger.info(`Optimized batch scrape request for ${urls.length} URLs`);

      // Validate all URLs
      const tasks = [];
      for (const url of urls) {
        try {
          new URL(url);
          tasks.push({ url });
        } catch (urlError) {
          logger.warn(`Invalid URL in batch: ${url}`);
        }
      }

      if (tasks.length === 0) {
        res.status(400).json({
          status: 400,
          message: "No valid URLs found",
          payload: {
            success: false,
            error: "No valid URLs in the batch"
          }
        });
        return;
      }

      // Execute batch scraping
      const results = await performanceMonitor.timeOperation(
        'batch_scrape_execution',
        () => this.scraperService.scrapeBatch(tasks),
        { urlCount: tasks.length }
      );

      performanceMonitor.stopOperation(operationId, { 
        urlCount: tasks.length,
        success: true,
        dataSize: JSON.stringify(results).length
      });

      res.status(200).json({
        status: 200,
        message: "OK",
        payload: {
          success: true,
          data: results,
          metadata: {
            totalRequested: urls.length,
            totalProcessed: tasks.length,
            totalResults: results.length
          }
        }
      });

    } catch (error) {
      performanceMonitor.stopOperation(operationId, { 
        success: false, 
        error: (error as Error).message 
      });

      logger.error("Error in batch scrape:", error);
      
      res.status(500).json({
        status: 500,
        message: "Internal server error",
        payload: {
          success: false,
          error: (error as Error).message
        }
      });
    }
  }

  /**
   * Get performance statistics
   */
  async getPerformanceStats(req: Request, res: Response): Promise<void> {
    try {
      const allStats = performanceMonitor.getAllStats();
      const memoryUsage = performanceMonitor.getCurrentMemoryUsage();
      const cacheStats = this.scraperService.getCacheStats();

      const statsObject: Record<string, any> = {};
      for (const [operation, stats] of allStats) {
        statsObject[operation] = stats;
      }

      res.status(200).json({
        status: 200,
        message: "OK",
        payload: {
          success: true,
          data: {
            operationStats: statsObject,
            memoryUsage,
            cacheStats,
            uptime: Math.round(process.uptime())
          }
        }
      });

    } catch (error) {
      logger.error("Error getting performance stats:", error);
      
      res.status(500).json({
        status: 500,
        message: "Internal server error",
        payload: {
          success: false,
          error: (error as Error).message
        }
      });
    }
  }

  /**
   * Clear cache and performance metrics
   */
  async clearCache(req: Request, res: Response): Promise<void> {
    try {
      this.scraperService.clearCache();
      performanceMonitor.clear();

      res.status(200).json({
        status: 200,
        message: "Cache and metrics cleared",
        payload: {
          success: true,
          data: {
            message: "Cache and performance metrics have been cleared"
          }
        }
      });

    } catch (error) {
      logger.error("Error clearing cache:", error);
      
      res.status(500).json({
        status: 500,
        message: "Internal server error",
        payload: {
          success: false,
          error: (error as Error).message
        }
      });
    }
  }

  /**
   * Health check endpoint with performance info
   */
  async healthCheck(req: Request, res: Response): Promise<void> {
    try {
      const memoryUsage = performanceMonitor.getCurrentMemoryUsage();
      const cacheStats = this.scraperService.getCacheStats();
      const uptime = Math.round(process.uptime());

      // Simple health check - consider unhealthy if memory usage is too high
      const isHealthy = memoryUsage.heapUsed < 500; // 500MB threshold

      res.status(isHealthy ? 200 : 503).json({
        status: isHealthy ? 200 : 503,
        message: isHealthy ? "Healthy" : "Unhealthy",
        payload: {
          success: isHealthy,
          data: {
            status: isHealthy ? "healthy" : "unhealthy",
            uptime: `${uptime}s`,
            memoryUsage: `${memoryUsage.heapUsed}MB`,
            cacheSize: cacheStats.size,
            timestamp: new Date().toISOString()
          }
        }
      });

    } catch (error) {
      logger.error("Error in health check:", error);
      
      res.status(503).json({
        status: 503,
        message: "Service unavailable",
        payload: {
          success: false,
          error: (error as Error).message
        }
      });
    }
  }
}

// Export singleton instance
export const optimizedScraperController = new OptimizedScraperController();
export default optimizedScraperController;
