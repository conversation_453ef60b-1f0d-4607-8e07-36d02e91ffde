/**
 * Optimized API routes with performance improvements
 */

import { Router } from 'express';
import { optimizedScraperController } from './optimized-scraper.controller';
import logger from '../utils/logger';

const router = Router();

/**
 * Middleware for request logging and timing
 */
router.use((req, res, next) => {
  const startTime = Date.now();
  const originalSend = res.send;
  
  res.send = function(data) {
    const duration = Date.now() - startTime;
    logger.info(`${req.method} ${req.path} - ${res.statusCode} - ${duration}ms`);
    return originalSend.call(this, data);
  };
  
  next();
});

/**
 * Rate limiting middleware (simple implementation)
 */
const rateLimitMap = new Map<string, { count: number; resetTime: number }>();
const RATE_LIMIT = 100; // requests per minute
const RATE_WINDOW = 60 * 1000; // 1 minute

router.use((req, res, next) => {
  const clientIp = req.ip || req.connection.remoteAddress || 'unknown';
  const now = Date.now();
  
  const clientData = rateLimitMap.get(clientIp);
  
  if (!clientData || now > clientData.resetTime) {
    // Reset or initialize
    rateLimitMap.set(clientIp, {
      count: 1,
      resetTime: now + RATE_WINDOW
    });
    next();
  } else if (clientData.count < RATE_LIMIT) {
    // Increment count
    clientData.count++;
    next();
  } else {
    // Rate limit exceeded
    res.status(429).json({
      status: 429,
      message: "Too many requests",
      payload: {
        success: false,
        error: "Rate limit exceeded. Please try again later."
      }
    });
  }
});

/**
 * POST /api/v2/scrape
 * Optimized single URL scraping
 */
router.post('/scrape', async (req, res) => {
  await optimizedScraperController.scrapeUrl(req, res);
});

/**
 * POST /api/v2/scrape/batch
 * Optimized batch URL scraping
 */
router.post('/scrape/batch', async (req, res) => {
  await optimizedScraperController.scrapeBatch(req, res);
});

/**
 * GET /api/v2/performance
 * Get performance statistics
 */
router.get('/performance', async (req, res) => {
  await optimizedScraperController.getPerformanceStats(req, res);
});

/**
 * POST /api/v2/cache/clear
 * Clear cache and performance metrics
 */
router.post('/cache/clear', async (req, res) => {
  await optimizedScraperController.clearCache(req, res);
});

/**
 * GET /api/v2/health
 * Health check with performance info
 */
router.get('/health', async (req, res) => {
  await optimizedScraperController.healthCheck(req, res);
});

/**
 * GET /api/v2/status
 * Detailed status information
 */
router.get('/status', async (req, res) => {
  try {
    const memoryUsage = process.memoryUsage();
    const uptime = process.uptime();
    const cpuUsage = process.cpuUsage();
    
    res.status(200).json({
      status: 200,
      message: "OK",
      payload: {
        success: true,
        data: {
          service: "Optimized Totalads Scraper",
          version: "2.0.0",
          uptime: Math.round(uptime),
          memory: {
            heapUsed: Math.round(memoryUsage.heapUsed / 1024 / 1024),
            heapTotal: Math.round(memoryUsage.heapTotal / 1024 / 1024),
            external: Math.round(memoryUsage.external / 1024 / 1024),
            rss: Math.round(memoryUsage.rss / 1024 / 1024)
          },
          cpu: {
            user: cpuUsage.user,
            system: cpuUsage.system
          },
          nodeVersion: process.version,
          platform: process.platform,
          timestamp: new Date().toISOString()
        }
      }
    });
  } catch (error) {
    res.status(500).json({
      status: 500,
      message: "Internal server error",
      payload: {
        success: false,
        error: (error as Error).message
      }
    });
  }
});

/**
 * Error handling middleware
 */
router.use((error: Error, req: any, res: any, next: any) => {
  logger.error(`Unhandled error in ${req.method} ${req.path}:`, error);
  
  res.status(500).json({
    status: 500,
    message: "Internal server error",
    payload: {
      success: false,
      error: error.message
    }
  });
});

export default router;
