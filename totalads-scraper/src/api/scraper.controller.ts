/**
 * Controller for scraper API endpoints
 */
import { Request, Response } from 'express';
import { z } from 'zod';
import { scraperService } from '../core/scraper.service';
import logger from '../utils/logger';
import { expressAsyncHandler } from 'totalads-shared';

// Validation schema for URL requests
export const ScrapeURLDataSchema = z.object({
  url: z.string().url({ message: "Invalid URL" }),
});

/**
 * Controller class for scraper API endpoints
 */
export class ScraperController {
  /**
   * Scrape a URL and return the extracted data
   */
  async scrapeUrl(req: Request, res: Response): Promise<void> {
    try {
      const { url } = req.body;

      logger.info(`API request received to scrape URL: ${url}`);
      const result = await scraperService.scrape(url);
      
      res.status(200).json({
        success: true,
        data: result
      });
    } catch (error) {
      logger.error(`Error in scrape controller: ${(error as Error).message}`);
      
      res.status(500).json({
        success: false,
        error: (error as Error).message || 'An error occurred during scraping'
      });
    }
  }

  /**
   * Health check endpoint to verify the scraper service is running
   */
  async healthCheck(req: Request, res: Response): Promise<void> {
    res.status(200).json({
      status: 'healthy',
      timestamp: new Date().toISOString()
    });
  }
}

// Export a singleton instance
export const scraperController = new ScraperController();
export default scraperController;
