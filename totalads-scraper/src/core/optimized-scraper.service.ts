/**
 * Optimized scraper service with performance improvements
 */

import { Page } from 'puppeteer';
import { <PERSON>rape<PERSON><PERSON>ult, EnhancedScrapeResult, BrowserTask } from '../models/scraper.model';
import logger from '../utils/logger';
import { handleError } from '../utils/error-handler';
import { <PERSON><PERSON>er<PERSON>ontroller } from '../utils/scraper/browser-controller';
import { ContentExtractor } from '../utils/scraper/content-extractor';
import { TableHandler } from '../utils/scraper/table-handler';
import { DataProcessorService } from '../processors/data-processor.service';
import { extractEnhancedAboutData, findAboutAndTeamPages } from '../extractors/enhanced-scraper';

/**
 * Simple in-memory cache for performance optimization
 */
class ScraperCache {
  private cache = new Map<string, any>();
  private readonly maxSize = 100;
  private readonly ttl = 5 * 60 * 1000; // 5 minutes

  set(key: string, value: any): void {
    // Simple LRU eviction
    if (this.cache.size >= this.maxSize) {
      const firstKey = this.cache.keys().next().value;
      this.cache.delete(firstKey);
    }
    
    this.cache.set(key, {
      value,
      timestamp: Date.now()
    });
  }

  get(key: string): any | null {
    const item = this.cache.get(key);
    if (!item) return null;
    
    // Check TTL
    if (Date.now() - item.timestamp > this.ttl) {
      this.cache.delete(key);
      return null;
    }
    
    return item.value;
  }

  clear(): void {
    this.cache.clear();
  }
}

/**
 * Performance metrics tracking
 */
interface PerformanceMetrics {
  startTime: number;
  navigationTime?: number;
  extractionTime?: number;
  totalTime?: number;
  cacheHits: number;
  cacheMisses: number;
}

/**
 * Optimized scraper service with parallel processing and caching
 */
export class OptimizedScraperService {
  private browserController: BrowserController;
  private contentExtractor: ContentExtractor;
  private tableHandler: TableHandler;
  private dataProcessorService: DataProcessorService;
  private cache: ScraperCache;

  constructor() {
    this.browserController = new BrowserController();
    this.contentExtractor = new ContentExtractor();
    this.tableHandler = new TableHandler();
    this.dataProcessorService = new DataProcessorService();
    this.cache = new ScraperCache();
    logger.info("Optimized scraper service initialized");
  }

  /**
   * Main scraping method with performance optimizations
   */
  async scrape(task: BrowserTask): Promise<ScrapeResult> {
    const metrics: PerformanceMetrics = {
      startTime: Date.now(),
      cacheHits: 0,
      cacheMisses: 0
    };

    const { url } = task;
    logger.info(`Starting optimized scrape for ${url}`);

    try {
      // Check cache first
      const cacheKey = `scrape:${url}`;
      const cachedResult = this.cache.get(cacheKey);
      if (cachedResult) {
        metrics.cacheHits++;
        logger.info(`Cache hit for ${url}`);
        return cachedResult;
      }
      metrics.cacheMisses++;

      // Execute optimized scraping with parallel operations
      const result = await this.browserController.executeWithPage<ScrapeResult>(
        task.url,
        async (page) => {
          metrics.navigationTime = Date.now() - metrics.startTime;
          
          // Parallel extraction of basic data
          const [metadata, links] = await Promise.all([
            this.contentExtractor.getTitleAndDesc(page),
            this.contentExtractor.extractLinks(page)
          ]);

          // Extract HTML content and process in parallel
          const [htmlText, aboutData] = await Promise.all([
            this.contentExtractor.convertHTMLToText(page),
            this.extractOptimizedAboutData(page, task.url, links)
          ]);

          // Process contact details from the extracted text
          const contactDetails = this.dataProcessorService.extractContactDetails(htmlText);

          metrics.extractionTime = Date.now() - metrics.startTime - (metrics.navigationTime || 0);

          return {
            title: metadata.title,
            desc: metadata.desc,
            nestedLinks: links,
            text: htmlText,
            contactDetails,
            aboutData
          };
        },
        {
          waitUntil: ["domcontentloaded"], // Faster loading
          timeout: 15000 // Reduced timeout
        }
      );

      metrics.totalTime = Date.now() - metrics.startTime;
      
      // Cache the result
      this.cache.set(cacheKey, result);
      
      // Log performance metrics
      logger.info(`Optimized scrape completed for ${url}`, {
        totalTime: metrics.totalTime,
        navigationTime: metrics.navigationTime,
        extractionTime: metrics.extractionTime,
        cacheHits: metrics.cacheHits,
        cacheMisses: metrics.cacheMisses
      });

      return result;
    } catch (error) {
      const errorMessage = `Failed to scrape URL: ${url}`;
      handleError(error as Error, 'OptimizedScraperService.scrape');
      
      return {
        title: null,
        desc: null,
        nestedLinks: [],
        text: `Error scraping content: ${(error as Error).message}`,
        contactDetails: {},
        aboutData: {}
      };
    }
  }

  /**
   * Optimized about data extraction with smart page selection
   */
  private async extractOptimizedAboutData(
    page: Page, 
    url: string, 
    links: string[]
  ): Promise<any> {
    try {
      // Extract from current page first
      const directAboutInfo = await extractEnhancedAboutData(page, url);
      
      // If we have sufficient data, skip additional pages
      if (this.isAboutDataSufficient(directAboutInfo)) {
        logger.info(`Sufficient about data found on main page for ${url}`);
        return directAboutInfo;
      }

      // Find about pages but limit to most promising ones
      const aboutPages = await findAboutAndTeamPages(page, url, links);
      if (aboutPages.length === 0) {
        return directAboutInfo;
      }

      // Select only the most promising about page (first one)
      const bestAboutPage = aboutPages[0];
      logger.info(`Extracting additional data from: ${bestAboutPage}`);

      try {
        // Navigate to the best about page
        await page.goto(bestAboutPage, {
          waitUntil: "domcontentloaded",
          timeout: 10000 // Fast timeout for about pages
        });

        const additionalAboutInfo = await extractEnhancedAboutData(page, bestAboutPage);
        
        // Merge the data
        return this.mergeAboutData(directAboutInfo, additionalAboutInfo);
      } catch (aboutPageError) {
        logger.warn(`Failed to extract from about page ${bestAboutPage}: ${aboutPageError}`);
        return directAboutInfo;
      }
    } catch (error) {
      logger.error("Error in extractOptimizedAboutData:", error);
      return {};
    }
  }

  /**
   * Check if about data is sufficient to skip additional pages
   */
  private isAboutDataSufficient(aboutData: any): boolean {
    return !!(
      aboutData.companyDescription &&
      (aboutData.teamMembers?.length || 0) > 0 &&
      (aboutData.officeLocations?.length || 0) > 0
    );
  }

  /**
   * Merge about data from multiple sources
   */
  private mergeAboutData(primary: any, secondary: any): any {
    return {
      ...primary,
      companyDescription: primary.companyDescription || secondary.companyDescription,
      foundingInfo: primary.foundingInfo || secondary.foundingInfo,
      missionStatement: primary.missionStatement || secondary.missionStatement,
      visionStatement: primary.visionStatement || secondary.visionStatement,
      companyValues: [...(primary.companyValues || []), ...(secondary.companyValues || [])],
      awards: [...(primary.awards || []), ...(secondary.awards || [])],
      industries: [...(primary.industries || []), ...(secondary.industries || [])],
      globalPresence: primary.globalPresence || secondary.globalPresence,
      officeLocations: [...(primary.officeLocations || []), ...(secondary.officeLocations || [])],
      certifications: [...(primary.certifications || []), ...(secondary.certifications || [])],
      keyPoints: [...(primary.keyPoints || []), ...(secondary.keyPoints || [])],
      teamMembers: secondary.teamMembers?.length > 0 ? secondary.teamMembers : primary.teamMembers || []
    };
  }

  /**
   * Batch scraping with concurrency control
   */
  async scrapeBatch(tasks: BrowserTask[]): Promise<ScrapeResult[]> {
    logger.info(`Starting batch scrape for ${tasks.length} URLs`);
    
    const results = await Promise.allSettled(
      tasks.map(task => this.scrape(task))
    );

    return results.map((result, index) => {
      if (result.status === 'fulfilled') {
        return result.value;
      } else {
        logger.error(`Batch scrape failed for ${tasks[index].url}: ${result.reason}`);
        return {
          title: null,
          desc: null,
          nestedLinks: [],
          text: `Batch scrape error: ${result.reason}`,
          contactDetails: {},
          aboutData: {}
        };
      }
    });
  }

  /**
   * Clear cache manually
   */
  clearCache(): void {
    this.cache.clear();
    logger.info("Scraper cache cleared");
  }

  /**
   * Get cache statistics
   */
  getCacheStats(): { size: number; maxSize: number } {
    return {
      size: this.cache['cache'].size,
      maxSize: this.cache['maxSize']
    };
  }
}
