/**
 * Core scraper service that orchestrates the scraping process
 */

import { ScrapeR<PERSON>ult, EnhancedScrapeResult, BrowserTask } from '../models/scraper.model';
import logger from '../utils/logger';
import { handleError } from '../utils/error-handler';
import { <PERSON>rowserController } from '../utils/scraper/browser-controller';
import { ContentExtractor } from '../utils/scraper/content-extractor';
import { TableHandler } from '../utils/scraper/table-handler';
import { DataProcessor } from '../utils/scraper/data-processor';
import { DataProcessorService } from '../processors/data-processor.service';
import { Page } from "puppeteer";
import { extractAboutInfo, extractCompanyInfoFromSite, findAboutAndTeamPages, mergeCompanyInfo } from "../extractors/about-info";

/**
 * ScraperService is the main orchestrator for scraping operations
 * It coordinates between browser control, content extraction, and data processing
 */
export class ScraperService {
  private browserController: BrowserController;
  private contentExtractor: ContentExtractor;
  private tableHandler: TableHandler;
  private dataProcessor: DataProcessor;
  private dataProcessorService: DataProcessorService;

  constructor() {
    this.browserController = new BrowserController();
    this.contentExtractor = new ContentExtractor();
    this.tableHandler = new TableHandler();
    this.dataProcessor = new DataProcessor();
    this.dataProcessorService = new DataProcessorService();
    
    logger.info('Scraper service initialized');
  }

  /**
   * Initialize the scraper
   */
  async initialize(): Promise<void> {
    try {
      logger.info('Initializing scraper service...');
      // Initialize browser controller with cluster
      if (!this.browserController.cluster) {
        await this.browserController.initCluster();
      }
      logger.info('Scraper service initialized successfully');
    } catch (error) {
      handleError(error as Error, 'ScraperService.initialize');
      throw new Error('Failed to initialize scraper service');
    }
  }

  /**
   * Scrape a URL to extract all relevant information
   * @param url URL to scrape
   * @returns Scraped result data
   */
  async scrape(url: string): Promise<ScrapeResult> {
    const startTime = Date.now();
    logger.info(`Starting scrape operation for URL: ${url}`);
    
    try {
      // Normalize the URL
      const normalizedUrl = this.dataProcessor.normalizeURL(url);
      
      // Create a task for the browser controller
      const task: BrowserTask = {
        url: normalizedUrl,
        taskId: `task_${Date.now()}`,
        options: {
          followRedirects: true,
          timeout: 60000
        }
      };
      
      // Execute all browser operations in a single cluster context
      // to prevent "Session closed" errors
      const result = await this.browserController.executeWithPage<ScrapeResult>(
        task.url,
        async (page) => {
          // Extract basic metadata (title, description)
          const metadata = await this.contentExtractor.getTitleAndDesc(page);
          
          // Extract links
          const links = await this.contentExtractor.extractLinks(page);
          
          // Extract and convert HTML to text
          const htmlText = await this.contentExtractor.convertHTMLToText(page);
          
          // Extract tables from the page
          const tables = await this.tableHandler.extractTablesAndReplacePlaceholders(page);
          
          // Use the extracted tables with the HTML text
          let textWithPlaceholders = htmlText;
          
          // Generate final text with tables formatted and inserted
          // If we have tables, we'll process them; otherwise use the original text
          const finalText = tables && tables.length > 0 
            ? this.tableHandler.replaceTablePlaceholders(tables, textWithPlaceholders)
            : textWithPlaceholders;
          
          // Extract contact information using the enhanced data processor service
          const contactDetails = this.dataProcessorService.extractContactDetails(finalText);
          
          // Extract about data directly from the current page
          logger.info(`Extracting about data from: ${task.url}`);
          const directAboutInfo = await extractAboutInfo(page, task.url);
          
          // Find about and team pages for deeper extraction
          logger.info(`Finding about and team pages from: ${task.url}`);
          const aboutPages = await findAboutAndTeamPages(page, task.url, links);
          let aboutData = directAboutInfo;
          
          // If we found additional about/team pages, extract data from them
          if (aboutPages && aboutPages.length > 0) {
            logger.info(`Found ${aboutPages.length} about/team pages to extract`); 
            // Extract company info from found pages (limited to 2 to avoid long processing)
            const config = { maxPagesToVisit: 2, timeout: 30000 };
            const companyInfo = await extractCompanyInfoFromSite(page, task.url, aboutPages, config);
            // Merge with direct extraction results
            aboutData = mergeCompanyInfo(directAboutInfo, companyInfo);
          }
          
          // Generate and return the final result
          return {
            title: metadata.title,
            desc: metadata.desc,
            nestedLinks: links,
            text: finalText,
            contactDetails,
            aboutData
          };
        }
      );
      
      const processingTime = Date.now() - startTime;
      logger.info(`Scrape completed for ${url} in ${processingTime}ms`);
      
      return result;
    } catch (error) {
      const errorMessage = `Failed to scrape URL: ${url}`;
      handleError(error as Error, 'ScraperService.scrape');
      
      // Return a partial result with error information
      return {
        title: null,
        desc: null,
        nestedLinks: [],
        text: `Error scraping content: ${(error as Error).message}`,
        contactDetails: {},
        aboutData: {}
      };
    }
  }
  
  /**
   * Clean up resources
   */
  async shutdown(): Promise<void> {
    try {
      logger.info('Shutting down scraper service...');
      // Close the cluster if it exists
      if (this.browserController.cluster) {
        await this.browserController.cluster.close();
      }
      logger.info('Scraper service shut down successfully');
    } catch (error) {
      handleError(error as Error, 'ScraperService.shutdown');
    }
  }
}

// Export a singleton instance
export const scraperService = new ScraperService();
export default scraperService;
