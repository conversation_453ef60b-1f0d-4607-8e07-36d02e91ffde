import dotenv from 'dotenv';
import path from 'path';
import { fileURLToPath } from 'url';

import logger from '../utils/logger';

// Load environment variables
dotenv.config();

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Default paths for different platforms
const DEFAULT_WINDOWS_PATH =
	"C:\\Users\\<USER>\\.cache\\puppeteer\\chrome\\win64-121.0.6167.85\\chrome-win64\\chrome.exe";
const DEFAULT_LINUX_PATH = "/usr/bin/chromium";
const DEFAULT_MAC_PATH =
	"/Applications/Google Chrome.app/Contents/MacOS/Google Chrome";

// Determine default path based on platform
const getDefaultChromePath = () => {
	switch (process.platform) {
		case "win32":
			return DEFAULT_WINDOWS_PATH;
		case "darwin":
			return DEFAULT_MAC_PATH;
		default:
			return DEFAULT_LINUX_PATH;
	}
};

/**
 * Browser configuration settings
 */
export const browserConfig = {
	executablePath: process.env.CHROME_PATH || getDefaultChromePath(),
	headless: process.env.HEADLESS !== "false",
	userDataDir:
		process.env.USER_DATA_DIR ||
		path.join(__dirname, "..", "..", "puppeteer_temp"),
	timeout: parseInt(process.env.BROWSER_TIMEOUT || "30000", 10), // Reduced timeout for faster failures
	maxConcurrency: parseInt(process.env.MAX_CONCURRENCY || "8", 10), // Increased concurrency
	navigationTimeout: parseInt(process.env.NAVIGATION_TIMEOUT || "15000", 10), // Faster navigation timeout
	waitForSelector: parseInt(process.env.WAIT_FOR_SELECTOR || "5000", 10), // Faster selector waiting
};

logger.info(`Browser config loaded: 
  - Executable path: ${browserConfig.executablePath}
  - Headless mode: ${browserConfig.headless}
  - User data directory: ${browserConfig.userDataDir}
  - Timeout: ${browserConfig.timeout}ms
  - Max concurrency: ${browserConfig.maxConcurrency}`);

/**
 * Browser launch arguments
 */
export const browserArgs = [
	"--disable-features=site-per-process",
	"--disable-gpu",
	"--disable-dev-shm-usage",
	"--disable-setuid-sandbox",
	"--no-first-run",
	"--no-sandbox",
	"--no-zygote",
	"--deterministic-fetch",
	"--disable-features=IsolateOrigins",
	"--disable-site-isolation-trials",
	"--disable-accelerated-2d-canvas",
	// Performance optimizations
	...performanceArgs,
];

/**
 * Resource types to abort during navigation for better performance
 */
export const resourceTypesToBlock = [
	"image",
	"media",
	"font",
	"texttrack",
	"eventsource",
	"websocket",
	"manifest",
	"other",
];

/**
 * Additional performance-focused browser arguments
 */
export const performanceArgs = [
	"--disable-background-timer-throttling",
	"--disable-backgrounding-occluded-windows",
	"--disable-renderer-backgrounding",
	"--disable-features=TranslateUI",
	"--disable-ipc-flooding-protection",
	"--disable-default-apps",
	"--disable-extensions",
	"--disable-component-extensions-with-background-pages",
	"--disable-background-networking",
	"--disable-sync",
	"--metrics-recording-only",
	"--no-default-browser-check",
	"--no-first-run",
	"--disable-default-apps",
	"--disable-popup-blocking",
	"--disable-prompt-on-repost",
	"--disable-hang-monitor",
	"--disable-client-side-phishing-detection",
	"--disable-component-update",
	"--disable-domain-reliability",
];
