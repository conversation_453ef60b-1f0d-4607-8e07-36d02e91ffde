const puppeteer = require('puppeteer');

// Performance test for the optimized scraper
async function testOptimizedScraper() {
  console.log('🚀 Starting optimized scraper performance test...\n');
  
  const browser = await puppeteer.launch({
    headless: true,
    args: [
      '--no-sandbox', 
      '--disable-setuid-sandbox',
      '--disable-dev-shm-usage',
      '--disable-gpu',
      '--disable-features=site-per-process',
      '--disable-background-timer-throttling',
      '--disable-backgrounding-occluded-windows',
      '--disable-renderer-backgrounding',
      '--disable-features=TranslateUI',
      '--disable-ipc-flooding-protection',
      '--disable-default-apps',
      '--disable-extensions'
    ]
  });
  
  const testUrls = [
    'https://www.ezrankings.com/about-us.html',
    'https://www.ezrankings.com/',
    'https://www.google.com'
  ];

  const results = [];
  
  try {
    for (const url of testUrls) {
      console.log(`📊 Testing: ${url}`);
      const startTime = Date.now();
      
      const page = await browser.newPage();
      
      // Set up optimized request interception
      await page.setRequestInterception(true);
      page.on('request', request => {
        const resourceType = request.resourceType();
        if (['image', 'media', 'font', 'texttrack', 'eventsource', 'websocket', 'manifest', 'other'].includes(resourceType)) {
          request.abort();
        } else {
          request.continue();
        }
      });
      
      // Navigate with optimized settings
      const navigationStart = Date.now();
      await page.goto(url, {
        waitUntil: 'domcontentloaded',
        timeout: 15000
      });
      const navigationTime = Date.now() - navigationStart;
      
      // Single-pass optimized extraction
      const extractionStart = Date.now();
      const result = await page.evaluate(() => {
        // Extract metadata
        let title = document.head.querySelector("title")?.innerHTML || null;
        let desc = null;
        
        document.head.querySelectorAll("meta").forEach((meta) => {
          const metaPropertyName = 
            meta.getAttribute("name") || 
            meta.getAttribute("property") || 
            "";
          
          if (["title", "og:title"].includes(metaPropertyName)) {
            title = meta.getAttribute("content");
          } else if (["description", "og:description"].includes(metaPropertyName)) {
            desc = meta.getAttribute("content");
          }
        });

        // Extract links
        const anchors = document.querySelectorAll("a");
        const links = [];
        
        anchors.forEach((anchor) => {
          if (anchor.href && anchor.href.startsWith('http')) {
            links.push(anchor.href);
          }
        });

        // Extract team members (optimized for EZ Rankings)
        const teamMembers = [];
        const designationContainers = document.querySelectorAll('.designation');
        
        designationContainers.forEach((designationDiv) => {
          const nameElement = designationDiv.querySelector('h3');
          const titleElement = designationDiv.querySelector('h5');
          
          if (nameElement && nameElement.textContent) {
            const member = {
              name: nameElement.textContent.trim(),
              title: titleElement ? titleElement.textContent.trim() : undefined,
              contact: {}
            };
            
            // Extract contact info
            const links = designationDiv.querySelectorAll('a');
            links.forEach((link) => {
              if (link.href) {
                if (link.href.includes('linkedin')) {
                  member.contact.linkedin = link.href;
                } else if (link.href.includes('mailto:')) {
                  member.contact.email = link.href.replace('mailto:', '');
                } else if (link.href.includes('skype:')) {
                  member.contact.skype = link.href;
                }
              }
            });
            
            if (Object.keys(member.contact).length === 0) {
              delete member.contact;
            }
            
            teamMembers.push(member);
          }
        });

        // Extract office locations
        const officeLocations = [];
        const officeElements = document.querySelectorAll('[class*="office"], .footer-colom');
        officeElements.forEach((element) => {
          if (element.textContent && element.textContent.toLowerCase().includes('office')) {
            const text = element.textContent.trim();
            if (text.length > 20 && text.length < 200) {
              officeLocations.push(text);
            }
          }
        });

        // Remove unnecessary elements for text extraction
        const elementsToRemove = document.querySelectorAll(
          'script, style, nav, header, footer, .advertisement, .ads, .social-share'
        );
        elementsToRemove.forEach(el => el.remove());

        // Get text content
        const textContent = document.body.textContent || '';
        const cleanedText = textContent.replace(/\s+/g, ' ').trim();

        return {
          metadata: { title, desc },
          links: Array.from(new Set(links)),
          text: cleanedText.substring(0, 1000) + '...', // Truncate for demo
          aboutData: {
            teamMembers,
            officeLocations,
            globalPresence: textContent.toLowerCase().includes('global') || 
                           textContent.toLowerCase().includes('international') ||
                           officeLocations.length > 1
          }
        };
      });
      
      const extractionTime = Date.now() - extractionStart;
      const totalTime = Date.now() - startTime;
      
      await page.close();
      
      const testResult = {
        url,
        navigationTime,
        extractionTime,
        totalTime,
        dataExtracted: {
          hasTitle: !!result.metadata.title,
          hasDescription: !!result.metadata.desc,
          linksCount: result.links.length,
          textLength: result.text.length,
          teamMembersCount: result.aboutData.teamMembers.length,
          officeLocationsCount: result.aboutData.officeLocations.length,
          globalPresence: result.aboutData.globalPresence
        }
      };
      
      results.push(testResult);
      
      console.log(`  ⏱️  Navigation: ${navigationTime}ms`);
      console.log(`  🔍 Extraction: ${extractionTime}ms`);
      console.log(`  🎯 Total: ${totalTime}ms`);
      console.log(`  📈 Data: ${result.links.length} links, ${result.aboutData.teamMembers.length} team members`);
      console.log('');
    }
    
    // Performance summary
    console.log('📊 PERFORMANCE SUMMARY');
    console.log('========================');
    
    const totalTests = results.length;
    const avgNavigationTime = results.reduce((sum, r) => sum + r.navigationTime, 0) / totalTests;
    const avgExtractionTime = results.reduce((sum, r) => sum + r.extractionTime, 0) / totalTests;
    const avgTotalTime = results.reduce((sum, r) => sum + r.totalTime, 0) / totalTests;
    
    console.log(`Tests completed: ${totalTests}`);
    console.log(`Average navigation time: ${Math.round(avgNavigationTime)}ms`);
    console.log(`Average extraction time: ${Math.round(avgExtractionTime)}ms`);
    console.log(`Average total time: ${Math.round(avgTotalTime)}ms`);
    
    const fastestTest = results.reduce((min, r) => r.totalTime < min.totalTime ? r : min);
    const slowestTest = results.reduce((max, r) => r.totalTime > max.totalTime ? r : max);
    
    console.log(`Fastest: ${fastestTest.url} (${fastestTest.totalTime}ms)`);
    console.log(`Slowest: ${slowestTest.url} (${slowestTest.totalTime}ms)`);
    
    // Data extraction summary
    console.log('\n📋 DATA EXTRACTION SUMMARY');
    console.log('============================');
    
    results.forEach((result, index) => {
      console.log(`${index + 1}. ${result.url}`);
      console.log(`   Title: ${result.dataExtracted.hasTitle ? '✅' : '❌'}`);
      console.log(`   Description: ${result.dataExtracted.hasDescription ? '✅' : '❌'}`);
      console.log(`   Links: ${result.dataExtracted.linksCount}`);
      console.log(`   Team Members: ${result.dataExtracted.teamMembersCount}`);
      console.log(`   Office Locations: ${result.dataExtracted.officeLocationsCount}`);
      console.log(`   Global Presence: ${result.dataExtracted.globalPresence ? '✅' : '❌'}`);
      console.log('');
    });
    
    console.log('🎉 Optimized scraper test completed successfully!');
    
  } catch (error) {
    console.error('❌ Error during test:', error);
  } finally {
    await browser.close();
  }
}

// Run the test
testOptimizedScraper().catch(console.error);
